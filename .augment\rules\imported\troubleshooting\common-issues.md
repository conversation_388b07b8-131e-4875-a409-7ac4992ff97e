---
type: "agent_requested"
description: "Example description"
---
# 🔧 Problemas Comuns - Auto-Instalador V3 Lite

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Documentar problemas comuns e suas soluções  

---

## 🚨 **PROBLEMAS DE INICIALIZAÇÃO**

### **1. <PERSON><PERSON>: "Electron failed to start"**
**Sintomas:**
- Aplicação não inicia
- Erro no console: `Error: spawn ENOENT`
- Tela branca ou crash imediato

**Causas Possíveis:**
- Node.js não instalado ou versão incompatível
- Dependências não instaladas
- Permissões insuficientes

**Soluções:**
```bash
# 1. Verificar versão do Node.js
node --version  # Deve ser >= 18.0.0

# 2. Reinstalar dependências
rm -rf node_modules package-lock.json
npm install

# 3. Limpar cache do Electron
npm run clean
npx electron-rebuild

# 4. Windows: Executar como administrador
# Linux/macOS: Verificar permissões
chmod +x node_modules/.bin/electron
```

### **2. Erro: "Backend API não responde"**
**Sintomas:**
- Frontend carrega mas não exibe dados
- Erro 500 ou timeout nas requisições
- Logs mostram "Connection refused"

**Causas Possíveis:**
- Backend .NET não iniciou
- Porta ocupada (5000/5001)
- Firewall bloqueando conexão

**Soluções:**
```bash
# 1. Verificar se o backend está rodando
netstat -an | grep :5000

# 2. Iniciar backend manualmente
cd src/backend
dotnet run --project AutoInstalador.API

# 3. Verificar logs do backend
tail -f logs/app.log

# 4. Testar conectividade
curl http://localhost:5000/health
```

---

## 🐳 **PROBLEMAS COM CONTAINERS**

### **3. Erro: "Docker daemon not running"**
**Sintomas:**
- Erro ao listar containers
- Mensagem "Cannot connect to Docker daemon"
- Interface mostra "Engine não disponível"

**Soluções:**
```bash
# Windows
# 1. Iniciar Docker Desktop
# 2. Verificar se o serviço está rodando
Get-Service docker

# Linux
# 1. Iniciar serviço Docker
sudo systemctl start docker
sudo systemctl enable docker

# 2. Adicionar usuário ao grupo docker
sudo usermod -aG docker $USER
# Fazer logout/login

# macOS
# 1. Iniciar Docker Desktop
# 2. Verificar status
docker info
```

### **4. Erro: "Podman socket not found"**
**Sintomas:**
- Podman não detectado
- Erro "No such file or directory: podman.sock"
- Containers Podman não aparecem

**Soluções:**
```bash
# Linux
# 1. Iniciar socket do Podman
systemctl --user start podman.socket
systemctl --user enable podman.socket

# 2. Verificar socket
ls -la /run/user/$UID/podman/podman.sock

# Windows (WSL)
# 1. Instalar Podman Desktop
# 2. Configurar socket
podman system service --time=0 unix:///tmp/podman.sock

# Verificar conectividade
podman --remote info
```

### **5. Erro: "Permission denied" ao acessar containers**
**Sintomas:**
- Erro 403 ao tentar iniciar/parar containers
- Mensagem "Permission denied"
- Operações falham silenciosamente

**Soluções:**
```bash
# Linux - Docker
sudo usermod -aG docker $USER
newgrp docker

# Linux - Podman
# Configurar rootless
podman system migrate
loginctl enable-linger $USER

# Windows
# Executar como administrador ou configurar Docker Desktop
# para permitir acesso sem privilégios elevados
```

---

## ⚡ **PROBLEMAS DE PERFORMANCE**

### **6. Alto uso de CPU/Memória**
**Sintomas:**
- Aplicação lenta ou travando
- Ventilador do computador acelerado
- Task Manager mostra alto uso de recursos

**Diagnóstico:**
```bash
# Verificar uso de recursos
# Windows
Get-Process | Where-Object {$_.ProcessName -like "*electron*"} | Select-Object ProcessName, CPU, WorkingSet

# Linux/macOS
ps aux | grep electron
top -p $(pgrep electron)
```

**Soluções:**
```bash
# 1. Reduzir intervalo de polling
# No arquivo de configuração, alterar:
# refetchInterval: 10000 (10 segundos em vez de 5)

# 2. Limitar número de containers monitorados
# Implementar paginação ou filtros

# 3. Otimizar queries do banco
# Verificar índices no SQLite
# Implementar cache Redis

# 4. Reduzir logs verbosos
# Alterar nível de log para WARNING em produção
```

### **7. Lentidão na inicialização**
**Sintomas:**
- Aplicação demora mais de 10 segundos para abrir
- Splash screen fica muito tempo visível
- Interface carrega parcialmente

**Soluções:**
```bash
# 1. Otimizar bundle
npm run build:production
# Verificar tamanho dos chunks

# 2. Implementar lazy loading
# Carregar componentes sob demanda

# 3. Otimizar banco de dados
# Executar VACUUM no SQLite
sqlite3 app.db "VACUUM;"

# 4. Limpar cache
rm -rf ~/.cache/auto-instalador-v3-lite
```

---

## 🌐 **PROBLEMAS DE REDE**

### **8. Erro: "Network timeout" nas requisições**
**Sintomas:**
- Requisições demoram muito ou falham
- Erro "Request timeout"
- Interface fica em loading infinito

**Soluções:**
```typescript
// 1. Aumentar timeout das requisições
const apiClient = axios.create({
  timeout: 30000, // 30 segundos
  retry: 3,
  retryDelay: 1000
});

// 2. Implementar retry automático
const retryConfig = {
  retries: 3,
  retryDelay: (retryCount) => retryCount * 1000,
  retryCondition: (error) => error.code === 'ECONNABORTED'
};
```

### **9. Erro: "CORS blocked" em desenvolvimento**
**Sintomas:**
- Erro CORS no console do navegador
- Requisições bloqueadas
- Funciona em produção mas não em dev

**Soluções:**
```csharp
// Backend - Program.cs
builder.Services.AddCors(options =>
{
    options.AddPolicy("Development", policy =>
    {
        policy.WithOrigins("http://localhost:3000")
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});

app.UseCors("Development");
```

---

## 💾 **PROBLEMAS DE BANCO DE DADOS**

### **10. Erro: "Database locked"**
**Sintomas:**
- Erro "database is locked"
- Operações de escrita falham
- Aplicação trava ao salvar dados

**Soluções:**
```bash
# 1. Verificar processos usando o banco
lsof app.db  # Linux/macOS
# Windows: Process Monitor

# 2. Forçar unlock (cuidado!)
sqlite3 app.db ".timeout 30000"

# 3. Verificar integridade
sqlite3 app.db "PRAGMA integrity_check;"

# 4. Backup e restore se necessário
sqlite3 app.db ".backup backup.db"
mv backup.db app.db
```

### **11. Erro: "Migration failed"**
**Sintomas:**
- Erro ao aplicar migrações
- Banco em estado inconsistente
- Aplicação não inicia após update

**Soluções:**
```bash
# 1. Verificar status das migrações
dotnet ef migrations list

# 2. Aplicar migrações manualmente
dotnet ef database update

# 3. Rollback se necessário
dotnet ef database update PreviousMigration

# 4. Reset completo (desenvolvimento)
dotnet ef database drop
dotnet ef database update
```

---

## 🔐 **PROBLEMAS DE SEGURANÇA**

### **12. Erro: "Context isolation violation"**
**Sintomas:**
- Erro de segurança no Electron
- Funcionalidades IPC não funcionam
- Console mostra warnings de segurança

**Soluções:**
```typescript
// main.ts - Configuração segura
const mainWindow = new BrowserWindow({
  webPreferences: {
    nodeIntegration: false,
    contextIsolation: true,
    enableRemoteModule: false,
    preload: path.join(__dirname, 'preload.js'),
    sandbox: true
  }
});

// preload.ts - Bridge seguro
contextBridge.exposeInMainWorld('electronAPI', {
  containers: {
    getAll: () => ipcRenderer.invoke('containers:getAll'),
    start: (id: string) => ipcRenderer.invoke('containers:start', id)
  }
});
```

---

## 🛠️ **FERRAMENTAS DE DIAGNÓSTICO**

### **Comandos Úteis para Diagnóstico**
```bash
# Verificar logs da aplicação
tail -f logs/app-*.log

# Verificar status dos serviços
systemctl status docker
systemctl status podman

# Verificar conectividade de rede
curl -v http://localhost:5000/health
telnet localhost 5000

# Verificar uso de recursos
htop
iotop
nethogs

# Verificar integridade dos arquivos
npm audit
dotnet list package --vulnerable
```

### **Logs Importantes**
```bash
# Frontend (Electron)
~/.config/auto-instalador-v3-lite/logs/

# Backend (.NET)
./logs/app-{date}.log

# Sistema
# Windows: Event Viewer
# Linux: /var/log/syslog
# macOS: Console.app
```

---

## 📞 **QUANDO BUSCAR AJUDA**

### **Informações para Incluir no Report**
1. **Versão da aplicação**
2. **Sistema operacional e versão**
3. **Versões do Docker/Podman**
4. **Logs relevantes**
5. **Passos para reproduzir o problema**
6. **Screenshots se aplicável**

### **Template de Bug Report**
```markdown
## Descrição do Problema
[Descreva o problema claramente]

## Ambiente
- SO: [Windows 11 / Ubuntu 22.04 / macOS 13]
- Versão da App: [3.0.0-lite]
- Docker: [24.0.5]
- Podman: [4.6.1]

## Passos para Reproduzir
1. [Primeiro passo]
2. [Segundo passo]
3. [Terceiro passo]

## Comportamento Esperado
[O que deveria acontecer]

## Comportamento Atual
[O que está acontecendo]

## Logs
```
[Cole os logs relevantes aqui]
```

## Screenshots
[Se aplicável]
```

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Problemas Comuns  
**✨ Status:** Guia de Troubleshooting Completo**
