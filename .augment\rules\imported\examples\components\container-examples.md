---
type: "agent_requested"
description: "Example description"
---
# 🧩 Exemplos de Componentes - Containers

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Exemplos práticos de componentes para containers  

---

## 🎯 **COMPONENTE PRINCIPAL - CONTAINER DASHBOARD**

### **ContainerDashboard.tsx**
```typescript
import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useContainers, useContainerMutations } from '@/hooks/use-containers';
import { useUIStore } from '@/store/ui';
import { Container, ContainerAction } from '@/types/containers';
import { ContainerList } from './ContainerList';
import { ContainerStats } from './ContainerStats';
import { ContainerLogs } from './ContainerLogs';
import { ContainerControls } from './ContainerControls';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, Plus, Filter } from 'lucide-react';

interface ContainerDashboardProps {
  className?: string;
}

export const ContainerDashboard: React.FC<ContainerDashboardProps> = ({
  className = ''
}) => {
  // Estados locais
  const [selectedContainer, setSelectedContainer] = useState<Container | null>(null);
  const [showAll, setShowAll] = useState(false);
  const [activeTab, setActiveTab] = useState<'list' | 'stats' | 'logs'>('list');
  
  // Hooks customizados
  const { data: containers, isLoading, error, refetch } = useContainers({ 
    all: showAll,
    refetchInterval: 5000 
  });
  
  const { startContainer, stopContainer, restartContainer } = useContainerMutations();
  const { addNotification } = useUIStore();
  
  // Callbacks
  const handleContainerSelect = useCallback((container: Container) => {
    setSelectedContainer(container);
    setActiveTab('stats');
  }, []);
  
  const handleContainerAction = useCallback(async (
    container: Container, 
    action: ContainerAction
  ) => {
    try {
      let result = false;
      
      switch (action) {
        case 'start':
          result = await startContainer.mutateAsync(container.id);
          break;
        case 'stop':
          result = await stopContainer.mutateAsync(container.id);
          break;
        case 'restart':
          result = await restartContainer.mutateAsync(container.id);
          break;
      }
      
      if (result) {
        addNotification({
          type: 'success',
          title: 'Sucesso',
          message: `Container ${container.name} ${action} executado com sucesso`
        });
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Erro',
        message: `Falha ao executar ${action} no container ${container.name}`
      });
    }
  }, [startContainer, stopContainer, restartContainer, addNotification]);
  
  const handleRefresh = useCallback(() => {
    refetch();
    addNotification({
      type: 'info',
      title: 'Atualizado',
      message: 'Lista de containers atualizada'
    });
  }, [refetch, addNotification]);
  
  // Render de loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        >
          <RefreshCw className="w-8 h-8 text-blue-500" />
        </motion.div>
        <span className="ml-2 text-gray-600">Carregando containers...</span>
      </div>
    );
  }
  
  // Render de erro
  if (error) {
    return (
      <Card className="p-6 border-red-200 bg-red-50">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-red-800 mb-2">
            Erro ao carregar containers
          </h3>
          <p className="text-red-600 mb-4">{error.message}</p>
          <Button onClick={handleRefresh} variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Tentar novamente
          </Button>
        </div>
      </Card>
    );
  }
  
  return (
    <motion.div
      className={`container-dashboard ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {/* Header com controles */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900">
            Gerenciamento de Containers
          </h1>
          <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
            {containers?.length || 0} containers
          </span>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAll(!showAll)}
          >
            <Filter className="w-4 h-4 mr-2" />
            {showAll ? 'Apenas ativos' : 'Todos'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          
          <Button size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Novo Container
          </Button>
        </div>
      </div>
      
      {/* Layout principal */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Lista de containers */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <ContainerList
              containers={containers || []}
              selectedContainer={selectedContainer}
              onContainerSelect={handleContainerSelect}
              onContainerAction={handleContainerAction}
            />
          </Card>
        </div>
        
        {/* Painel lateral */}
        <div className="space-y-6">
          <AnimatePresence mode="wait">
            {selectedContainer ? (
              <motion.div
                key="container-details"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold">
                      {selectedContainer.name}
                    </h3>
                    <ContainerControls
                      container={selectedContainer}
                      onAction={handleContainerAction}
                    />
                  </div>
                  
                  <Tabs value={activeTab} onValueChange={setActiveTab}>
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="list">Info</TabsTrigger>
                      <TabsTrigger value="stats">Stats</TabsTrigger>
                      <TabsTrigger value="logs">Logs</TabsTrigger>
                    </TabsList>
                    
                    <TabsContent value="list" className="mt-4">
                      <ContainerInfo container={selectedContainer} />
                    </TabsContent>
                    
                    <TabsContent value="stats" className="mt-4">
                      <ContainerStats containerId={selectedContainer.id} />
                    </TabsContent>
                    
                    <TabsContent value="logs" className="mt-4">
                      <ContainerLogs containerId={selectedContainer.id} />
                    </TabsContent>
                  </Tabs>
                </Card>
              </motion.div>
            ) : (
              <motion.div
                key="no-selection"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                <Card className="p-6 text-center">
                  <div className="text-gray-500">
                    <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                      <Plus className="w-8 h-8" />
                    </div>
                    <h3 className="text-lg font-medium mb-2">
                      Selecione um container
                    </h3>
                    <p className="text-sm">
                      Clique em um container da lista para ver detalhes,
                      estatísticas e logs em tempo real.
                    </p>
                  </div>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
};

// Componente auxiliar para informações do container
const ContainerInfo: React.FC<{ container: Container }> = ({ container }) => (
  <div className="space-y-3">
    <div>
      <label className="text-sm font-medium text-gray-500">ID</label>
      <p className="text-sm font-mono bg-gray-100 p-2 rounded">
        {container.id.substring(0, 12)}...
      </p>
    </div>
    
    <div>
      <label className="text-sm font-medium text-gray-500">Imagem</label>
      <p className="text-sm">{container.imageName}</p>
    </div>
    
    <div>
      <label className="text-sm font-medium text-gray-500">Status</label>
      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
        container.status === 'running' 
          ? 'bg-green-100 text-green-800'
          : 'bg-gray-100 text-gray-800'
      }`}>
        {container.status}
      </span>
    </div>
    
    <div>
      <label className="text-sm font-medium text-gray-500">Engine</label>
      <p className="text-sm capitalize">{container.engine}</p>
    </div>
    
    <div>
      <label className="text-sm font-medium text-gray-500">Criado em</label>
      <p className="text-sm">
        {new Date(container.createdAt).toLocaleString('pt-BR')}
      </p>
    </div>
  </div>
);

export default ContainerDashboard;
```

---

## 📊 **COMPONENTE DE ESTATÍSTICAS**

### **ContainerStats.tsx**
```typescript
import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { containerApi } from '@/services/api/containers';
import { ContainerStats as StatsType } from '@/types/containers';
import { Card } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Cpu, MemoryStick, Network, HardDrive } from 'lucide-react';

interface ContainerStatsProps {
  containerId: string;
  className?: string;
}

export const ContainerStats: React.FC<ContainerStatsProps> = ({
  containerId,
  className = ''
}) => {
  const [previousStats, setPreviousStats] = useState<StatsType | null>(null);
  
  const { data: stats, isLoading } = useQuery({
    queryKey: ['container-stats', containerId],
    queryFn: () => containerApi.getStats(containerId),
    refetchInterval: 2000, // Atualiza a cada 2 segundos
    enabled: !!containerId,
  });
  
  useEffect(() => {
    if (stats) {
      setPreviousStats(stats);
    }
  }, [stats]);
  
  if (isLoading && !previousStats) {
    return (
      <div className="animate-pulse space-y-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-16 bg-gray-200 rounded"></div>
        ))}
      </div>
    );
  }
  
  const currentStats = stats || previousStats;
  
  if (!currentStats) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Cpu className="w-12 h-12 mx-auto mb-2 opacity-50" />
        <p>Estatísticas não disponíveis</p>
      </div>
    );
  }
  
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  const formatNetworkSpeed = (bytes: number) => {
    return formatBytes(bytes) + '/s';
  };
  
  return (
    <motion.div
      className={`container-stats space-y-4 ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* CPU Usage */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <Cpu className="w-5 h-5 text-blue-500 mr-2" />
            <span className="font-medium">CPU</span>
          </div>
          <span className="text-sm font-mono">
            {currentStats.cpuUsagePercent.toFixed(1)}%
          </span>
        </div>
        <Progress 
          value={currentStats.cpuUsagePercent} 
          className="h-2"
          indicatorClassName={
            currentStats.cpuUsagePercent > 80 ? 'bg-red-500' :
            currentStats.cpuUsagePercent > 60 ? 'bg-yellow-500' : 'bg-green-500'
          }
        />
      </Card>
      
      {/* Memory Usage */}
      <Card className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center">
            <MemoryStick className="w-5 h-5 text-purple-500 mr-2" />
            <span className="font-medium">Memória</span>
          </div>
          <span className="text-sm font-mono">
            {formatBytes(currentStats.memoryUsageBytes)} / {formatBytes(currentStats.memoryLimitBytes)}
          </span>
        </div>
        <Progress 
          value={currentStats.memoryUsagePercent} 
          className="h-2"
          indicatorClassName={
            currentStats.memoryUsagePercent > 80 ? 'bg-red-500' :
            currentStats.memoryUsagePercent > 60 ? 'bg-yellow-500' : 'bg-blue-500'
          }
        />
        <div className="text-xs text-gray-500 mt-1">
          {currentStats.memoryUsagePercent.toFixed(1)}% utilizado
        </div>
      </Card>
      
      {/* Network I/O */}
      <Card className="p-4">
        <div className="flex items-center mb-3">
          <Network className="w-5 h-5 text-green-500 mr-2" />
          <span className="font-medium">Rede</span>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <div className="text-xs text-gray-500 mb-1">Download</div>
            <div className="text-sm font-mono text-green-600">
              ↓ {formatNetworkSpeed(currentStats.networkRxBytes)}
            </div>
          </div>
          <div>
            <div className="text-xs text-gray-500 mb-1">Upload</div>
            <div className="text-sm font-mono text-blue-600">
              ↑ {formatNetworkSpeed(currentStats.networkTxBytes)}
            </div>
          </div>
        </div>
      </Card>
      
      {/* Timestamp */}
      <div className="text-xs text-gray-500 text-center">
        Última atualização: {new Date(currentStats.timestamp).toLocaleTimeString('pt-BR')}
      </div>
    </motion.div>
  );
};

export default ContainerStats;
```

---

## 📝 **COMPONENTE DE LOGS**

### **ContainerLogs.tsx**
```typescript
import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { containerApi } from '@/services/api/containers';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Download, Pause, Play, Trash2, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface ContainerLogsProps {
  containerId: string;
  className?: string;
}

export const ContainerLogs: React.FC<ContainerLogsProps> = ({
  containerId,
  className = ''
}) => {
  const [isPaused, setIsPaused] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [autoScroll, setAutoScroll] = useState(true);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const bottomRef = useRef<HTMLDivElement>(null);
  
  const { data: logs, isLoading } = useQuery({
    queryKey: ['container-logs', containerId],
    queryFn: () => containerApi.getLogs(containerId, { tail: 100 }),
    refetchInterval: isPaused ? false : 2000,
    enabled: !!containerId,
  });
  
  // Auto-scroll para o final quando novos logs chegam
  useEffect(() => {
    if (autoScroll && bottomRef.current) {
      bottomRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs, autoScroll]);
  
  const handleDownloadLogs = async () => {
    try {
      const allLogs = await containerApi.getLogs(containerId, { tail: 0 });
      const blob = new Blob([allLogs], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `container-${containerId}-logs.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Erro ao baixar logs:', error);
    }
  };
  
  const handleClearLogs = () => {
    // Implementar limpeza de logs se suportado pelo engine
    console.log('Clear logs não implementado ainda');
  };
  
  const filteredLogs = logs
    ? logs.split('\n').filter(line => 
        searchTerm === '' || line.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : [];
  
  const formatLogLine = (line: string, index: number) => {
    // Detectar nível de log pela cor
    let className = 'text-gray-800';
    if (line.includes('ERROR') || line.includes('FATAL')) {
      className = 'text-red-600';
    } else if (line.includes('WARN')) {
      className = 'text-yellow-600';
    } else if (line.includes('INFO')) {
      className = 'text-blue-600';
    } else if (line.includes('DEBUG')) {
      className = 'text-gray-500';
    }
    
    return (
      <motion.div
        key={index}
        initial={{ opacity: 0, x: -10 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.1, delay: index * 0.01 }}
        className={`font-mono text-xs leading-relaxed ${className} hover:bg-gray-50 px-2 py-1 rounded`}
      >
        {line}
      </motion.div>
    );
  };
  
  return (
    <motion.div
      className={`container-logs ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      {/* Controles */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPaused(!isPaused)}
          >
            {isPaused ? (
              <Play className="w-4 h-4 mr-1" />
            ) : (
              <Pause className="w-4 h-4 mr-1" />
            )}
            {isPaused ? 'Retomar' : 'Pausar'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleDownloadLogs}
          >
            <Download className="w-4 h-4 mr-1" />
            Baixar
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleClearLogs}
          >
            <Trash2 className="w-4 h-4 mr-1" />
            Limpar
          </Button>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Filtrar logs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 w-48"
              size="sm"
            />
          </div>
          
          <label className="flex items-center space-x-2 text-sm">
            <input
              type="checkbox"
              checked={autoScroll}
              onChange={(e) => setAutoScroll(e.target.checked)}
              className="rounded"
            />
            <span>Auto-scroll</span>
          </label>
        </div>
      </div>
      
      {/* Área de logs */}
      <Card className="p-0 h-96">
        <ScrollArea className="h-full p-4" ref={scrollAreaRef}>
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-pulse text-gray-500">
                Carregando logs...
              </div>
            </div>
          ) : filteredLogs.length > 0 ? (
            <div className="space-y-1">
              {filteredLogs.map((line, index) => formatLogLine(line, index))}
              <div ref={bottomRef} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-full text-gray-500">
              {searchTerm ? (
                <div className="text-center">
                  <Search className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>Nenhum log encontrado para "{searchTerm}"</p>
                </div>
              ) : (
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-2 bg-gray-100 rounded-full flex items-center justify-center">
                    📝
                  </div>
                  <p>Nenhum log disponível</p>
                </div>
              )}
            </div>
          )}
        </ScrollArea>
      </Card>
      
      {/* Status */}
      <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
        <span>
          {filteredLogs.length} linha(s) {searchTerm && `(filtrado de ${logs?.split('\n').length || 0})`}
        </span>
        <span>
          {isPaused ? 'Pausado' : 'Atualizando a cada 2s'}
        </span>
      </div>
    </motion.div>
  );
};

export default ContainerLogs;
```

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Exemplos de Componentes  
**✨ Status:** Exemplos Práticos Completos**
