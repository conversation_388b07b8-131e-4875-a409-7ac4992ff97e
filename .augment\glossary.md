# 📚 Glossário Técnico - Auto-Instalador V3 Lite

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Glossário técnico em Português Brasileiro  

---

## 🔤 **A**

**API (Interface de Programação de Aplicações)**
- Interface que permite comunicação entre diferentes componentes de software
- No projeto: REST API em .NET 8.0 para comunicação frontend-backend

**ASP.NET Core**
- Framework web da Microsoft para desenvolvimento de APIs e aplicações web
- Usado no backend do Auto-Instalador V3 Lite

**Auto-updater (Atualizador Automático)**
- Sistema que verifica e instala atualizações automaticamente
- Implementado no Electron para manter a aplicação sempre atualizada

---

## 🔤 **B**

**Backend (Retaguarda)**
- Parte do sistema responsável pela lógica de negócio e processamento de dados
- No projeto: API .NET 8.0 que gerencia containers e dados

**Bridge (Ponte)**
- Componente que conecta diferentes partes do sistema de forma segura
- No Electron: preload scripts que conectam main e renderer processes

**Bundle (Pacote)**
- Arquivo final gerado após o processo de build contendo todo o código otimizado
- Criado pelo Vite para o frontend e Electron Builder para distribuição

---

## 🔤 **C**

**Cache (Armazenamento Temporário)**
- Armazenamento temporário de dados para melhorar performance
- Implementado com Redis e React Query no projeto

**Clean Architecture (Arquitetura Limpa)**
- Padrão arquitetural que separa responsabilidades em camadas bem definidas
- Aplicado no backend com camadas Core, Application, Infrastructure e API

**Container (Contêiner)**
- Unidade de software que empacota código e dependências
- Gerenciado pelo Docker ou Podman no Auto-Instalador

**CORS (Cross-Origin Resource Sharing)**
- Mecanismo de segurança que controla requisições entre diferentes origens
- Configurado no backend para permitir comunicação com o frontend

**CSP (Content Security Policy)**
- Política de segurança que previne ataques XSS
- Implementada no Electron para maior segurança

---

## 🔤 **D**

**Daemon (Serviço em Segundo Plano)**
- Processo que roda continuamente em segundo plano
- Docker daemon gerencia containers no sistema

**DTO (Data Transfer Object)**
- Objeto usado para transferir dados entre camadas da aplicação
- Usado para comunicação entre frontend e backend

**Dependency Injection (Injeção de Dependência)**
- Padrão que permite inversão de controle e melhor testabilidade
- Implementado nativamente no .NET Core

---

## 🔤 **E**

**Electron**
- Framework para criar aplicações desktop usando tecnologias web
- Base da interface desktop do Auto-Instalador V3 Lite

**Entity Framework (EF)**
- ORM (Object-Relational Mapping) da Microsoft para .NET
- Usado para acesso ao banco de dados SQLite

**Engine (Motor)**
- Sistema responsável por executar containers
- Docker e Podman são os engines suportados

---

## 🔤 **F**

**Frontend (Interface)**
- Parte visual da aplicação com a qual o usuário interage
- Implementado com React 19.2.0 e Electron

**Framer Motion**
- Biblioteca para animações em React
- Usado para criar transições suaves na interface

---

## 🔤 **G**

**Garbage Collection (Coleta de Lixo)**
- Processo automático de liberação de memória não utilizada
- Otimizado no Node.js e .NET para melhor performance

---

## 🔤 **H**

**Hook (Gancho)**
- Função especial do React que permite usar estado e outros recursos
- Hooks customizados criados para gerenciar containers

**Hot Reload (Recarga Automática)**
- Atualização automática da aplicação durante desenvolvimento
- Implementado com Vite para o frontend

---

## 🔤 **I**

**IPC (Inter-Process Communication)**
- Comunicação entre processos diferentes
- Usado no Electron para comunicação entre main e renderer

**Interface**
- Contrato que define métodos que uma classe deve implementar
- Usado extensivamente no backend para inversão de dependência

---

## 🔤 **J**

**JSON (JavaScript Object Notation)**
- Formato de dados leve para troca de informações
- Usado para comunicação entre frontend e backend

---

## 🔤 **L**

**Lazy Loading (Carregamento Sob Demanda)**
- Técnica que carrega componentes apenas quando necessário
- Implementado no React para otimizar performance

**Logs (Registros)**
- Registros de eventos e atividades do sistema
- Coletados dos containers e da aplicação para monitoramento

---

## 🔤 **M**

**Main Process (Processo Principal)**
- Processo principal do Electron que gerencia janelas e recursos nativos
- Responsável por IPC e acesso ao sistema operacional

**Middleware**
- Software que atua como intermediário entre diferentes componentes
- Usado no ASP.NET Core para tratamento de requisições

**Migration (Migração)**
- Script que atualiza a estrutura do banco de dados
- Gerenciado pelo Entity Framework

---

## 🔤 **N**

**Node.js**
- Runtime JavaScript para execução fora do navegador
- Base do Electron e ferramentas de desenvolvimento

---

## 🔤 **O**

**ORM (Object-Relational Mapping)**
- Técnica que mapeia objetos para tabelas de banco de dados
- Entity Framework Core usado no projeto

---

## 🔤 **P**

**Podman**
- Engine de containers alternativo ao Docker
- Suportado como opção no Auto-Instalador

**Preload Script**
- Script executado antes do carregamento da página no Electron
- Usado para criar bridge seguro entre main e renderer

---

## 🔤 **Q**

**Query (Consulta)**
- Operação de busca de dados
- React Query usado para gerenciar estado do servidor

---

## 🔤 **R**

**React**
- Biblioteca JavaScript para construção de interfaces de usuário
- Versão 19.2.0 com novos recursos como Actions e useOptimistic

**Redis**
- Banco de dados em memória usado para cache
- Implementado para melhorar performance do backend

**Renderer Process (Processo Renderizador)**
- Processo do Electron que executa a interface web
- Onde roda a aplicação React

**Repository Pattern (Padrão Repository)**
- Padrão que abstrai o acesso a dados
- Implementado no backend para separar lógica de persistência

**REST API**
- Estilo arquitetural para APIs web
- Implementado no backend para comunicação com frontend

---

## 🔤 **S**

**SQLite**
- Banco de dados relacional leve e embarcado
- Usado para persistência local de dados

**State Management (Gerenciamento de Estado)**
- Controle do estado da aplicação
- Implementado com Zustand e React Query

**System Tray (Bandeja do Sistema)**
- Área da barra de tarefas para ícones de aplicações
- Integração implementada no Electron

---

## 🔤 **T**

**Tailwind CSS**
- Framework CSS utilitário para estilização
- Versão 4.0.0-beta.1 usado no projeto

**TypeScript**
- Superset do JavaScript que adiciona tipagem estática
- Usado em todo o frontend para maior segurança de tipos

---

## 🔤 **U**

**UI (User Interface - Interface do Usuário)**
- Elementos visuais com os quais o usuário interage
- Construída com React e Tailwind CSS

**Use Case (Caso de Uso)**
- Classe que encapsula lógica de negócio específica
- Implementado na camada de aplicação do backend

---

## 🔤 **V**

**Vite**
- Ferramenta de build rápida para desenvolvimento frontend
- Usado para desenvolvimento e build do React

**Volume**
- Mecanismo de persistência de dados em containers
- Gerenciado através da interface do Auto-Instalador

---

## 🔤 **W**

**WebPreferences**
- Configurações de segurança do Electron
- Configurado para máxima segurança com context isolation

**Webpack**
- Bundler de módulos JavaScript (alternativa ao Vite)
- Vite escolhido por sua velocidade superior

---

## 🔤 **Z**

**Zustand**
- Biblioteca leve para gerenciamento de estado em React
- Usado para estado da interface e preferências do usuário

---

## 📖 **TERMOS ESPECÍFICOS DO PROJETO**

**Auto-Instalador V3 Lite**
- Nome da aplicação desktop para gerenciamento de containers
- Versão otimizada para hardware específico (i5 12ª Gen)

**Container Dashboard**
- Interface principal para visualização e controle de containers
- Componente React central da aplicação

**Container Engine**
- Sistema responsável por executar containers (Docker ou Podman)
- Abstração implementada no backend

**Engine Detection**
- Processo de detecção automática de engines de container instalados
- Implementado para identificar Docker e Podman

**IPC Bridge**
- Ponte segura de comunicação entre processos Electron
- Implementado com preload scripts para segurança

**Stats Collector**
- Componente responsável por coletar estatísticas de containers
- Implementado para monitoramento em tempo real

---

## 🔧 **COMANDOS E FERRAMENTAS**

**npm (Node Package Manager)**
- Gerenciador de pacotes do Node.js
- Usado para instalar dependências do frontend

**dotnet CLI**
- Interface de linha de comando do .NET
- Usado para build e execução do backend

**docker CLI**
- Interface de linha de comando do Docker
- Integrado para controle de containers

**git**
- Sistema de controle de versão
- Usado para versionamento do código

---

## 📊 **MÉTRICAS E MONITORAMENTO**

**CPU Usage (Uso de CPU)**
- Percentual de utilização do processador por container
- Coletado em tempo real para monitoramento

**Memory Usage (Uso de Memória)**
- Quantidade de RAM utilizada por container
- Monitorado com limites e alertas

**Network I/O (Entrada/Saída de Rede)**
- Tráfego de rede de containers
- Medido em bytes recebidos e enviados

**Logs Streaming**
- Transmissão contínua de logs de containers
- Implementado para monitoramento em tempo real

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Glossário Técnico  
**✨ Status:** Terminologia Completa em Português Brasileiro**
