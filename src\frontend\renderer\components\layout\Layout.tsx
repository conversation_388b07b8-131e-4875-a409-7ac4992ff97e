/**
 * Layout - Layout principal da aplicação
 * Auto-Instalador V3 Lite
 * 
 * @description Layout base com sidebar e header
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const location = useLocation();

  // Determinar se deve mostrar sidebar baseado na rota
  const shouldShowSidebar = !location.pathname.includes('/fullscreen');

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="flex h-screen bg-gray-900 text-white overflow-hidden">
      {/* Sidebar */}
      {shouldShowSidebar && (
        <div className={`
          transition-all duration-300 ease-in-out
          ${sidebarOpen ? 'w-64' : 'w-16'}
          flex-shrink-0
        `}>
          <Sidebar 
            isOpen={sidebarOpen}
            onToggle={toggleSidebar}
          />
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <Header 
          showSidebarToggle={shouldShowSidebar}
          sidebarOpen={sidebarOpen}
          onSidebarToggle={toggleSidebar}
        />

        {/* Main Content */}
        <main className="flex-1 overflow-hidden bg-gray-800">
          {children}
        </main>
      </div>
    </div>
  );
};
