/**
 * Tailwind CSS Configuration - Auto-Instalador V3 Lite
 * 
 * @description Configuração do Tailwind CSS com tema customizado
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/frontend/renderer/**/*.{js,ts,jsx,tsx}',
    './src/frontend/renderer/index.html'
  ],
  theme: {
    extend: {
      // Cores customizadas baseadas no template Docker Desktop
      colors: {
        // Grays customizados
        gray: {
          50: '#F9FAFB',
          100: '#F3F4F6',
          200: '#E5E7EB',
          300: '#D1D5DB',
          400: '#9CA3AF',
          500: '#6B7280',
          600: '#4B5563',
          700: '#374151',
          800: '#1F2937',
          900: '#111827',
          950: '#0B0F19'
        },
        // Blues customizados
        blue: {
          50: '#EFF6FF',
          100: '#DBEAFE',
          200: '#BFDBFE',
          300: '#93C5FD',
          400: '#60A5FA',
          500: '#3B82F6',
          600: '#2563EB',
          700: '#1D4ED8',
          800: '#1E40AF',
          900: '#1E3A8A',
          950: '#172554'
        },
        // Container status colors
        container: {
          running: '#10B981',
          stopped: '#EF4444',
          paused: '#F59E0B',
          restarting: '#3B82F6',
          dead: '#7F1D1D'
        }
      },
      
      // Fontes customizadas
      fontFamily: {
        sans: [
          'system-ui',
          '-apple-system',
          'BlinkMacSystemFont',
          '"Segoe UI"',
          'Roboto',
          '"Helvetica Neue"',
          'Arial',
          '"Noto Sans"',
          'sans-serif'
        ],
        mono: [
          '"SF Mono"',
          'Monaco',
          '"Cascadia Code"',
          '"Roboto Mono"',
          'Consolas',
          '"Liberation Mono"',
          '"Menlo"',
          'monospace'
        ]
      },

      // Espaçamentos customizados
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem'
      },

      // Larguras customizadas
      width: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem'
      },

      // Alturas customizadas
      height: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem'
      },

      // Border radius customizado
      borderRadius: {
        'xl': '0.75rem',
        '2xl': '1rem',
        '3xl': '1.5rem'
      },

      // Box shadows customizadas
      boxShadow: {
        'container': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'container-hover': '0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05)',
        'modal': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'glow-blue': '0 0 20px rgba(59, 130, 246, 0.3)',
        'glow-green': '0 0 20px rgba(16, 185, 129, 0.3)',
        'glow-red': '0 0 20px rgba(239, 68, 68, 0.3)'
      },

      // Animações customizadas
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'slide-left': 'slideLeft 0.3s ease-out',
        'slide-right': 'slideRight 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'bounce-subtle': 'bounceSubtle 0.6s ease-in-out',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'spin-slow': 'spin 3s linear infinite'
      },

      // Keyframes para animações
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' }
        },
        slideLeft: {
          '0%': { transform: 'translateX(10px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        slideRight: {
          '0%': { transform: 'translateX(-10px)', opacity: '0' },
          '100%': { transform: 'translateX(0)', opacity: '1' }
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' }
        },
        bounceSubtle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-2px)' }
        }
      },

      // Backdrop blur customizado
      backdropBlur: {
        xs: '2px'
      },

      // Z-index customizado
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100'
      }
    }
  },
  plugins: [
    // Plugin para forms
    require('@tailwindcss/forms')({
      strategy: 'class'
    }),
    
    // Plugin customizado para componentes
    function({ addComponents, theme }) {
      addComponents({
        '.btn': {
          display: 'inline-flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: `${theme('spacing.2')} ${theme('spacing.4')}`,
          fontSize: theme('fontSize.sm'),
          fontWeight: theme('fontWeight.medium'),
          borderRadius: theme('borderRadius.md'),
          transition: 'all 0.2s ease-in-out',
          cursor: 'pointer',
          '&:focus': {
            outline: 'none',
            boxShadow: `0 0 0 2px ${theme('colors.blue.500')}40`
          },
          '&:disabled': {
            opacity: '0.5',
            cursor: 'not-allowed'
          }
        },
        '.card': {
          backgroundColor: theme('colors.gray.800'),
          border: `1px solid ${theme('colors.gray.700')}`,
          borderRadius: theme('borderRadius.lg'),
          padding: theme('spacing.6'),
          boxShadow: theme('boxShadow.container')
        },
        '.input': {
          display: 'block',
          width: '100%',
          padding: `${theme('spacing.2')} ${theme('spacing.3')}`,
          backgroundColor: theme('colors.gray.700'),
          border: `1px solid ${theme('colors.gray.600')}`,
          borderRadius: theme('borderRadius.md'),
          color: theme('colors.white'),
          fontSize: theme('fontSize.sm'),
          '&::placeholder': {
            color: theme('colors.gray.400')
          },
          '&:focus': {
            outline: 'none',
            borderColor: theme('colors.blue.500'),
            boxShadow: `0 0 0 1px ${theme('colors.blue.500')}`
          }
        }
      })
    }
  ],
  
  // Configurações de dark mode
  darkMode: 'class',
  
  // Configurações de purge para produção
  purge: {
    enabled: process.env.NODE_ENV === 'production',
    content: [
      './src/frontend/renderer/**/*.{js,ts,jsx,tsx}',
      './src/frontend/renderer/index.html'
    ],
    options: {
      safelist: [
        'animate-spin',
        'animate-pulse',
        'animate-bounce',
        /^bg-container-/,
        /^text-container-/,
        /^border-container-/
      ]
    }
  }
};
