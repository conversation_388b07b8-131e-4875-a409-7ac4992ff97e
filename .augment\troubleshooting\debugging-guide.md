# 🐛 Guia de Debugging - Auto-Instalador V3 Lite

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Guia completo para debugging e diagnóstico  

---

## 🎯 **ESTRATÉGIAS DE DEBUGGING**

### **Abordagem Sistemática**
1. **Reproduzir o problema** de forma consistente
2. **Isolar a camada** onde o problema ocorre
3. **Verificar logs** relevantes
4. **Usar ferramentas** apropriadas para cada camada
5. **Testar hipóteses** de forma incremental

### **Camadas de Debugging**
```
🎨 Frontend (React/Electron)
   ├── React DevTools
   ├── Electron DevTools
   ├── Network Tab
   └── Console Logs

🏗️ Backend (.NET)
   ├── Visual Studio Debugger
   ├── dotnet CLI debugging
   ├── Structured Logs (Serilog)
   └── API Testing Tools

🐳 Container Layer
   ├── Docker/Podman CLI
   ├── Container Logs
   ├── Engine Status
   └── Network Inspection

💾 Data Layer
   ├── SQLite Browser
   ├── Redis CLI
   ├── Database Logs
   └── Query Analysis
```

---

## 🎨 **DEBUGGING FRONTEND**

### **React DevTools**
```bash
# Instalar React DevTools
# Chrome/Edge: React Developer Tools extension
# Firefox: React Developer Tools addon

# Debugging de componentes
1. Abrir DevTools (F12)
2. Aba "Components"
3. Selecionar componente
4. Inspecionar props e state
5. Verificar hooks e context
```

### **Electron DevTools**
```typescript
// main.ts - Habilitar DevTools em desenvolvimento
if (isDevelopment) {
  mainWindow.webContents.openDevTools();
}

// Debugging IPC
// main.ts
ipcMain.handle('debug-info', () => {
  console.log('IPC call received');
  return { timestamp: Date.now() };
});

// renderer
window.electronAPI.debugInfo().then(console.log);
```

### **Console Debugging**
```typescript
// Debugging de hooks customizados
export const useContainers = (options = {}) => {
  const result = useQuery({
    queryKey: ['containers', options],
    queryFn: () => {
      console.log('🔍 Fetching containers with options:', options);
      return containerApi.getAll(options);
    },
    onSuccess: (data) => {
      console.log('✅ Containers loaded:', data.length);
    },
    onError: (error) => {
      console.error('❌ Failed to load containers:', error);
    }
  });
  
  console.log('🎯 useContainers state:', {
    isLoading: result.isLoading,
    error: result.error,
    dataLength: result.data?.length
  });
  
  return result;
};

// Debugging de estado Zustand
export const useUIStore = create((set, get) => ({
  // ... state
  
  // Debug helper
  _debug: () => {
    console.log('🏪 Current UI State:', get());
  }
}));

// Usar no componente
const { _debug } = useUIStore();
_debug(); // Mostra estado atual
```

### **Network Debugging**
```typescript
// Interceptor para debugging de API calls
import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'http://localhost:5000/api'
});

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    console.log('🚀 API Request:', {
      method: config.method?.toUpperCase(),
      url: config.url,
      data: config.data,
      params: config.params
    });
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    console.log('✅ API Response:', {
      status: response.status,
      url: response.config.url,
      data: response.data
    });
    return response;
  },
  (error) => {
    console.error('❌ Response Error:', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.message,
      data: error.response?.data
    });
    return Promise.reject(error);
  }
);
```

---

## 🏗️ **DEBUGGING BACKEND**

### **Visual Studio / VS Code Debugging**
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug API",
      "type": "coreclr",
      "request": "launch",
      "program": "${workspaceFolder}/src/backend/src/AutoInstalador.API/bin/Debug/net8.0/AutoInstalador.API.dll",
      "args": [],
      "cwd": "${workspaceFolder}/src/backend/src/AutoInstalador.API",
      "stopAtEntry": false,
      "serverReadyAction": {
        "action": "openExternally",
        "pattern": "\\bNow listening on:\\s+(https?://\\S+)"
      },
      "env": {
        "ASPNETCORE_ENVIRONMENT": "Development"
      }
    }
  ]
}
```

### **Logging Estruturado**
```csharp
// Program.cs - Configuração de logging
builder.Services.AddSerilog((services, lc) => lc
    .ReadFrom.Configuration(builder.Configuration)
    .ReadFrom.Services(services)
    .Enrich.FromLogContext()
    .WriteTo.Console(outputTemplate: 
        "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}")
    .WriteTo.File("logs/app-.log", 
        rollingInterval: RollingInterval.Day,
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"));

// Uso em controllers
[HttpGet]
public async Task<ActionResult<IEnumerable<ContainerResponse>>> GetAll(
    [FromQuery] bool all = false)
{
    using var activity = _logger.BeginScope(new Dictionary<string, object>
    {
        ["Action"] = nameof(GetAll),
        ["Parameters"] = new { all }
    });
    
    _logger.LogInformation("Iniciando busca de containers");
    
    try
    {
        var containers = await _containerService.GetAllAsync(all);
        
        _logger.LogInformation("Containers encontrados: {Count}", containers.Count());
        
        var response = _mapper.Map<IEnumerable<ContainerResponse>>(containers);
        return Ok(response);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Erro ao buscar containers");
        return StatusCode(500, "Erro interno do servidor");
    }
}
```

### **Debugging de Serviços**
```csharp
// ContainerService.cs - Debug helpers
public class ContainerService : IContainerService
{
    private readonly ILogger<ContainerService> _logger;
    
    public async Task<IEnumerable<Container>> GetAllAsync(bool includeAll = false)
    {
        _logger.LogDebug("GetAllAsync called with includeAll: {IncludeAll}", includeAll);
        
        var engines = await _engineManager.GetAvailableEnginesAsync();
        _logger.LogDebug("Available engines: {Engines}", 
            string.Join(", ", engines.Select(e => e.Engine)));
        
        var allContainers = new List<Container>();
        
        foreach (var engine in engines)
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();
                var containers = await engine.ListContainersAsync(includeAll);
                stopwatch.Stop();
                
                _logger.LogDebug("Engine {Engine} returned {Count} containers in {ElapsedMs}ms",
                    engine.Engine, containers.Count(), stopwatch.ElapsedMilliseconds);
                
                allContainers.AddRange(containers);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get containers from engine {Engine}", 
                    engine.Engine);
            }
        }
        
        _logger.LogInformation("Total containers retrieved: {Count}", allContainers.Count);
        return allContainers.OrderBy(c => c.Name);
    }
}
```

### **API Testing com curl/Postman**
```bash
# Testar endpoints manualmente
# Health check
curl -X GET http://localhost:5000/health

# Listar containers
curl -X GET http://localhost:5000/api/containers \
  -H "Content-Type: application/json"

# Iniciar container
curl -X POST http://localhost:5000/api/containers/abc123/start \
  -H "Content-Type: application/json"

# Criar container
curl -X POST http://localhost:5000/api/containers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-container",
    "imageName": "nginx:latest",
    "engine": "docker"
  }'
```

---

## 🐳 **DEBUGGING CONTAINER LAYER**

### **Docker Debugging**
```bash
# Verificar status do Docker
docker info
docker version

# Listar containers
docker ps -a

# Inspecionar container específico
docker inspect <container_id>

# Logs de container
docker logs <container_id> --follow --tail 100

# Executar comando em container
docker exec -it <container_id> /bin/bash

# Verificar uso de recursos
docker stats <container_id>

# Verificar redes
docker network ls
docker network inspect bridge
```

### **Podman Debugging**
```bash
# Verificar status do Podman
podman info
podman version

# Listar containers
podman ps -a

# Inspecionar container
podman inspect <container_id>

# Logs de container
podman logs <container_id> --follow --tail 100

# Verificar socket (Linux)
ls -la /run/user/$UID/podman/podman.sock

# Testar API REST
curl --unix-socket /run/user/$UID/podman/podman.sock \
  http://localhost/v1.0.0/libpod/containers/json
```

### **Debugging de Integração**
```csharp
// DockerService.cs - Debug helpers
public async Task<IEnumerable<Container>> ListContainersAsync(bool all = false)
{
    _logger.LogDebug("Docker ListContainersAsync called with all: {All}", all);
    
    try
    {
        // Verificar conectividade
        await _dockerClient.System.PingAsync();
        _logger.LogDebug("Docker daemon ping successful");
        
        var parameters = new ContainersListParameters { All = all };
        var stopwatch = Stopwatch.StartNew();
        
        var dockerContainers = await _dockerClient.Containers.ListContainersAsync(parameters);
        stopwatch.Stop();
        
        _logger.LogDebug("Docker API returned {Count} containers in {ElapsedMs}ms",
            dockerContainers.Count, stopwatch.ElapsedMilliseconds);
        
        var mappedContainers = dockerContainers.Select(MapDockerContainerToContainer).ToList();
        
        _logger.LogDebug("Mapped containers: {ContainerNames}",
            string.Join(", ", mappedContainers.Select(c => c.Name)));
        
        return mappedContainers;
    }
    catch (DockerApiException ex)
    {
        _logger.LogError(ex, "Docker API error: {StatusCode} - {Message}",
            ex.StatusCode, ex.ResponseBody);
        throw;
    }
}
```

---

## 💾 **DEBUGGING DATA LAYER**

### **SQLite Debugging**
```bash
# Conectar ao banco
sqlite3 app.db

# Verificar tabelas
.tables

# Verificar schema
.schema containers

# Consultar dados
SELECT * FROM containers LIMIT 10;

# Verificar índices
.indices containers

# Analisar query plan
EXPLAIN QUERY PLAN SELECT * FROM containers WHERE status = 'running';

# Verificar integridade
PRAGMA integrity_check;
```

### **Entity Framework Debugging**
```csharp
// AppDbContext.cs - Logging de queries
protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
{
    if (_isDevelopment)
    {
        optionsBuilder
            .LogTo(Console.WriteLine, LogLevel.Information)
            .EnableSensitiveDataLogging()
            .EnableDetailedErrors();
    }
}

// Repository debugging
public async Task<Container?> GetByIdAsync(string id)
{
    _logger.LogDebug("Searching for container with ID: {ContainerId}", id);
    
    var container = await _context.Containers
        .Include(c => c.Stats)
        .FirstOrDefaultAsync(c => c.Id == id);
    
    if (container == null)
    {
        _logger.LogWarning("Container not found: {ContainerId}", id);
    }
    else
    {
        _logger.LogDebug("Container found: {ContainerName}", container.Name);
    }
    
    return container;
}
```

### **Redis Debugging**
```bash
# Conectar ao Redis
redis-cli

# Verificar chaves
KEYS *

# Verificar valor específico
GET "container_abc123"

# Verificar TTL
TTL "container_abc123"

# Monitorar comandos
MONITOR

# Informações do servidor
INFO memory
INFO stats
```

---

## 🔧 **FERRAMENTAS DE DEBUGGING**

### **Logs Centralizados**
```bash
# Visualizar logs em tempo real
tail -f logs/app-*.log | grep ERROR
tail -f logs/app-*.log | grep "container"

# Filtrar logs por nível
grep "ERROR\|FATAL" logs/app-*.log

# Buscar por padrões específicos
grep -n "ContainerService" logs/app-*.log
```

### **Performance Profiling**
```csharp
// Middleware para timing de requests
public class RequestTimingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<RequestTimingMiddleware> _logger;

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        
        await _next(context);
        
        stopwatch.Stop();
        
        if (stopwatch.ElapsedMilliseconds > 1000) // Log requests > 1s
        {
            _logger.LogWarning("Slow request: {Method} {Path} took {ElapsedMs}ms",
                context.Request.Method,
                context.Request.Path,
                stopwatch.ElapsedMilliseconds);
        }
    }
}
```

### **Health Checks**
```csharp
// Program.cs
builder.Services.AddHealthChecks()
    .AddCheck<DatabaseHealthCheck>("database")
    .AddCheck<DockerHealthCheck>("docker")
    .AddCheck<PodmanHealthCheck>("podman");

// Endpoint para verificar saúde
app.MapHealthChecks("/health", new HealthCheckOptions
{
    ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse
});
```

---

## 📊 **DEBUGGING DE PERFORMANCE**

### **Frontend Performance**
```typescript
// React Profiler
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration) {
  console.log('Component render:', {
    id,
    phase,
    actualDuration
  });
}

<Profiler id="ContainerDashboard" onRender={onRenderCallback}>
  <ContainerDashboard />
</Profiler>

// Memory usage monitoring
const measureMemory = () => {
  if ('memory' in performance) {
    console.log('Memory usage:', {
      used: Math.round(performance.memory.usedJSHeapSize / 1048576) + ' MB',
      total: Math.round(performance.memory.totalJSHeapSize / 1048576) + ' MB',
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1048576) + ' MB'
    });
  }
};
```

### **Backend Performance**
```csharp
// Middleware para profiling
public class ProfilingMiddleware
{
    public async Task InvokeAsync(HttpContext context)
    {
        using var activity = Activity.StartActivity("HTTP Request");
        activity?.SetTag("http.method", context.Request.Method);
        activity?.SetTag("http.url", context.Request.Path);
        
        var stopwatch = Stopwatch.StartNew();
        
        await _next(context);
        
        stopwatch.Stop();
        activity?.SetTag("duration_ms", stopwatch.ElapsedMilliseconds);
    }
}
```

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Guia de Debugging  
**✨ Status:** Guia Completo de Debugging e Diagnóstico**
