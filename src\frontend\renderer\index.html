<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Auto-Instalador V3 Lite - Gerenciador de containers e pacotes" />
  <meta name="author" content="Augment Agent" />
  
  <!-- Electron Security -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self' 'unsafe-inline' 'unsafe-eval';
    script-src 'self' 'unsafe-inline' 'unsafe-eval';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob:;
    font-src 'self' data:;
    connect-src 'self' ws: wss: http: https:;
  " />
  
  <title>Auto-Instalador V3 Lite</title>
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
  <link rel="icon" type="image/png" href="/favicon.png" />
  
  <!-- Preload critical resources -->
  <link rel="preload" href="/fonts/inter.woff2" as="font" type="font/woff2" crossorigin />
  
  <!-- Theme color for OS integration -->
  <meta name="theme-color" content="#111827" />
  
  <!-- Prevent zoom -->
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  
  <!-- Electron specific -->
  <style>
    /* Prevent text selection in UI elements */
    body {
      -webkit-user-select: none;
      -webkit-app-region: no-drag;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 'Helvetica Neue', sans-serif;
    }
    
    /* Allow text selection in content areas */
    input, textarea, [contenteditable] {
      -webkit-user-select: text;
    }
    
    /* Loading screen */
    #loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #111827;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 9999;
      color: white;
    }
    
    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid #374151;
      border-top: 3px solid #3B82F6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      margin-top: 16px;
      font-size: 14px;
      color: #9CA3AF;
    }
  </style>
</head>
<body>
  <!-- Loading screen -->
  <div id="loading">
    <div>
      <div class="loading-spinner"></div>
      <div class="loading-text">Carregando Auto-Instalador...</div>
    </div>
  </div>
  
  <!-- React app root -->
  <div id="root"></div>
  
  <!-- Remove loading screen when React loads -->
  <script>
    window.addEventListener('DOMContentLoaded', () => {
      // Remove loading screen after a short delay to ensure React has loaded
      setTimeout(() => {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.opacity = '0';
          loading.style.transition = 'opacity 0.3s ease-out';
          setTimeout(() => {
            loading.remove();
          }, 300);
        }
      }, 1000);
    });
    
    // Error handling
    window.addEventListener('error', (event) => {
      console.error('Erro global capturado:', event.error);
    });
    
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Promise rejeitada:', event.reason);
    });
  </script>
  
  <!-- React app entry point -->
  <script type="module" src="/index.tsx"></script>
</body>
</html>
