---
type: "agent_requested"
description: "Example description"
---
# 🐳 Integração Docker - Auto-Instalador V3 Lite

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Documentar integração completa com Docker  

---

## 🌟 **VISÃO GERAL DA INTEGRAÇÃO**

A integração com Docker no Auto-Instalador V3 Lite é implementada através de uma arquitetura em camadas que proporciona:
- **Detecção Automática:** Identifica instalações Docker existentes
- **API Unificada:** Interface consistente para operações Docker
- **Monitoramento Real-time:** Estatísticas e logs em tempo real
- **Gerenciamento Completo:** Ciclo de vida completo dos containers

### **Componentes da Integração**
```
Backend Integration:
├── DockerService.cs              # Implementação principal
├── DockerEngineDetector.cs       # Detecção e validação
├── DockerStatsCollector.cs       # Coleta de estatísticas
└── DockerLogStreamer.cs          # Streaming de logs

Frontend Integration:
├── docker-api.ts                 # Cliente API Docker
├── docker-hooks.ts               # Hooks React para Docker
├── docker-types.ts               # Tipos TypeScript
└── docker-utils.ts               # Utilitários Docker
```

---

## 🏗️ **IMPLEMENTAÇÃO BACKEND**

### **DockerService.cs - Serviço Principal**
```csharp
using Docker.DotNet;
using Docker.DotNet.Models;
using Microsoft.Extensions.Logging;
using AutoInstalador.Core.Interfaces;
using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Enums;

namespace AutoInstalador.Infrastructure.External.Containers;

public class DockerService : IContainerEngine
{
    private readonly DockerClient _dockerClient;
    private readonly ILogger<DockerService> _logger;
    private readonly DockerClientConfiguration _config;

    public ContainerEngine Engine => ContainerEngine.Docker;
    public bool IsAvailable { get; private set; }

    public DockerService(ILogger<DockerService> logger)
    {
        _logger = logger;
        
        // Configuração do cliente Docker
        _config = new DockerClientConfiguration();
        
        // Detectar endpoint automaticamente
        var endpoint = DetectDockerEndpoint();
        if (endpoint != null)
        {
            _config = new DockerClientConfiguration(endpoint);
            _dockerClient = _config.CreateClient();
            IsAvailable = true;
        }
        else
        {
            _logger.LogWarning("Docker endpoint não encontrado");
            IsAvailable = false;
        }
    }

    public async Task<IEnumerable<Container>> ListContainersAsync(bool all = false)
    {
        try
        {
            if (!IsAvailable)
                return Enumerable.Empty<Container>();

            var parameters = new ContainersListParameters { All = all };
            var dockerContainers = await _dockerClient.Containers.ListContainersAsync(parameters);

            return dockerContainers.Select(MapDockerContainerToContainer);
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Erro ao listar containers Docker");
            throw new ContainerEngineException("Falha ao listar containers Docker", ex);
        }
    }

    public async Task<Container> CreateContainerAsync(CreateContainerRequest request)
    {
        try
        {
            var createParams = new CreateContainerParameters
            {
                Image = request.ImageName,
                Name = request.Name,
                Env = request.Environment?.Select(kv => $"{kv.Key}={kv.Value}").ToList(),
                ExposedPorts = MapPortsToExposed(request.Ports),
                HostConfig = new HostConfig
                {
                    PortBindings = MapPortsToBindings(request.Ports),
                    Binds = request.Volumes?.Select(v => $"{v.HostPath}:{v.ContainerPath}").ToList(),
                    RestartPolicy = new RestartPolicy { Name = RestartPolicyKind.UnlessStopped }
                },
                WorkingDir = request.WorkingDirectory,
                Cmd = request.Command?.Split(' ').ToList()
            };

            var response = await _dockerClient.Containers.CreateContainerAsync(createParams);
            
            _logger.LogInformation("Container Docker criado: {ContainerId}", response.ID);

            return await GetContainerByIdAsync(response.ID);
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Erro ao criar container Docker: {ContainerName}", request.Name);
            throw new ContainerEngineException($"Falha ao criar container {request.Name}", ex);
        }
    }

    public async Task<bool> StartContainerAsync(string containerId)
    {
        try
        {
            var parameters = new ContainerStartParameters();
            var result = await _dockerClient.Containers.StartContainerAsync(containerId, parameters);
            
            _logger.LogInformation("Container Docker iniciado: {ContainerId}", containerId);
            return result;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Erro ao iniciar container Docker: {ContainerId}", containerId);
            return false;
        }
    }

    public async Task<bool> StopContainerAsync(string containerId, int timeoutSeconds = 30)
    {
        try
        {
            var parameters = new ContainerStopParameters
            {
                WaitBeforeKillSeconds = (uint)timeoutSeconds
            };
            
            var result = await _dockerClient.Containers.StopContainerAsync(containerId, parameters);
            
            _logger.LogInformation("Container Docker parado: {ContainerId}", containerId);
            return result;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Erro ao parar container Docker: {ContainerId}", containerId);
            return false;
        }
    }

    public async Task<bool> RemoveContainerAsync(string containerId, bool force = false)
    {
        try
        {
            var parameters = new ContainerRemoveParameters
            {
                Force = force,
                RemoveVolumes = true
            };
            
            await _dockerClient.Containers.RemoveContainerAsync(containerId, parameters);
            
            _logger.LogInformation("Container Docker removido: {ContainerId}", containerId);
            return true;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Erro ao remover container Docker: {ContainerId}", containerId);
            return false;
        }
    }

    public async Task<ContainerStats?> GetContainerStatsAsync(string containerId)
    {
        try
        {
            var parameters = new ContainerStatsParameters
            {
                Stream = false // Apenas uma leitura
            };

            var statsResponse = await _dockerClient.Containers.GetContainerStatsAsync(containerId, parameters);
            
            return MapDockerStatsToContainerStats(statsResponse, containerId);
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Erro ao obter estatísticas do container Docker: {ContainerId}", containerId);
            return null;
        }
    }

    public async Task<string> GetContainerLogsAsync(string containerId, int tail = 100)
    {
        try
        {
            var parameters = new ContainerLogsParameters
            {
                ShowStdout = true,
                ShowStderr = true,
                Tail = tail.ToString(),
                Timestamps = true
            };

            using var logsStream = await _dockerClient.Containers.GetContainerLogsAsync(containerId, parameters);
            using var reader = new StreamReader(logsStream);
            
            return await reader.ReadToEndAsync();
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Erro ao obter logs do container Docker: {ContainerId}", containerId);
            return string.Empty;
        }
    }

    // Métodos auxiliares privados
    private Uri? DetectDockerEndpoint()
    {
        // Windows: Named pipe
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return new Uri("npipe://./pipe/docker_engine");
        }
        
        // Linux/macOS: Unix socket
        var socketPath = "/var/run/docker.sock";
        if (File.Exists(socketPath))
        {
            return new Uri($"unix://{socketPath}");
        }
        
        // Fallback para TCP (Docker Desktop)
        try
        {
            var tcpEndpoint = new Uri("tcp://localhost:2375");
            // Testar conectividade
            return tcpEndpoint;
        }
        catch
        {
            return null;
        }
    }

    private Container MapDockerContainerToContainer(ContainerListResponse dockerContainer)
    {
        return new Container
        {
            Id = dockerContainer.ID,
            Name = dockerContainer.Names.FirstOrDefault()?.TrimStart('/') ?? "unknown",
            ImageName = dockerContainer.Image,
            Status = MapDockerStatusToContainerStatus(dockerContainer.State),
            Engine = ContainerEngine.Docker,
            CreatedAt = DateTimeOffset.FromUnixTimeSeconds(dockerContainer.Created).DateTime,
            Labels = dockerContainer.Labels ?? new Dictionary<string, string>(),
            Ports = MapDockerPortsToContainerPorts(dockerContainer.Ports)
        };
    }

    private ContainerStatus MapDockerStatusToContainerStatus(string dockerState)
    {
        return dockerState.ToLower() switch
        {
            "created" => ContainerStatus.Created,
            "running" => ContainerStatus.Running,
            "paused" => ContainerStatus.Paused,
            "restarting" => ContainerStatus.Restarting,
            "removing" => ContainerStatus.Removing,
            "exited" => ContainerStatus.Stopped,
            "dead" => ContainerStatus.Dead,
            _ => ContainerStatus.Stopped
        };
    }

    private ContainerStats MapDockerStatsToContainerStats(ContainerStatsResponse stats, string containerId)
    {
        // Calcular percentual de CPU
        var cpuDelta = stats.CPUStats.CPUUsage.TotalUsage - stats.PreCPUStats.CPUUsage.TotalUsage;
        var systemDelta = stats.CPUStats.SystemUsage - stats.PreCPUStats.SystemUsage;
        var cpuPercent = systemDelta > 0 ? (cpuDelta / (double)systemDelta) * stats.CPUStats.OnlineCPUs * 100.0 : 0.0;

        // Calcular uso de memória
        var memoryUsage = stats.MemoryStats.Usage;
        var memoryLimit = stats.MemoryStats.Limit;
        var memoryPercent = memoryLimit > 0 ? (memoryUsage / (double)memoryLimit) * 100.0 : 0.0;

        // Calcular I/O de rede
        var networkRx = stats.Networks?.Values.Sum(n => (long)n.RxBytes) ?? 0;
        var networkTx = stats.Networks?.Values.Sum(n => (long)n.TxBytes) ?? 0;

        return new ContainerStats
        {
            ContainerId = containerId,
            CpuUsagePercent = Math.Round(cpuPercent, 2),
            MemoryUsageBytes = (long)memoryUsage,
            MemoryLimitBytes = (long)memoryLimit,
            MemoryUsagePercent = Math.Round(memoryPercent, 2),
            NetworkRxBytes = networkRx,
            NetworkTxBytes = networkTx,
            Timestamp = DateTime.UtcNow
        };
    }

    public void Dispose()
    {
        _dockerClient?.Dispose();
    }
}
```

---

## 🔍 **DETECÇÃO E VALIDAÇÃO**

### **DockerEngineDetector.cs**
```csharp
public class DockerEngineDetector : IContainerEngineDetector
{
    private readonly ILogger<DockerEngineDetector> _logger;

    public async Task<ContainerEngineInfo?> DetectDockerAsync()
    {
        try
        {
            // Verificar se Docker está instalado
            var dockerPath = await FindDockerExecutableAsync();
            if (string.IsNullOrEmpty(dockerPath))
            {
                return null;
            }

            // Verificar versão
            var version = await GetDockerVersionAsync(dockerPath);
            if (string.IsNullOrEmpty(version))
            {
                return null;
            }

            // Verificar se daemon está rodando
            var isRunning = await IsDockerDaemonRunningAsync();

            return new ContainerEngineInfo
            {
                Engine = ContainerEngine.Docker,
                Version = version,
                IsInstalled = true,
                IsRunning = isRunning,
                ExecutablePath = dockerPath,
                InstallationPath = Path.GetDirectoryName(dockerPath)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao detectar Docker");
            return null;
        }
    }

    private async Task<string?> FindDockerExecutableAsync()
    {
        var possiblePaths = new[]
        {
            "docker", // PATH
            "/usr/bin/docker", // Linux padrão
            "/usr/local/bin/docker", // Linux alternativo
            "C:\\Program Files\\Docker\\Docker\\resources\\bin\\docker.exe", // Windows Docker Desktop
            "/Applications/Docker.app/Contents/Resources/bin/docker" // macOS Docker Desktop
        };

        foreach (var path in possiblePaths)
        {
            try
            {
                var result = await RunCommandAsync(path, "--version");
                if (result.ExitCode == 0)
                {
                    return path;
                }
            }
            catch
            {
                // Continuar tentando outros caminhos
            }
        }

        return null;
    }

    private async Task<string?> GetDockerVersionAsync(string dockerPath)
    {
        try
        {
            var result = await RunCommandAsync(dockerPath, "--version");
            if (result.ExitCode == 0)
            {
                // Extrair versão da saída: "Docker version 24.0.5, build ced0996"
                var match = Regex.Match(result.Output, @"Docker version (\d+\.\d+\.\d+)");
                return match.Success ? match.Groups[1].Value : null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao obter versão do Docker");
        }

        return null;
    }

    private async Task<bool> IsDockerDaemonRunningAsync()
    {
        try
        {
            using var client = new DockerClientConfiguration().CreateClient();
            await client.System.PingAsync();
            return true;
        }
        catch
        {
            return false;
        }
    }
}
```

---

## 🎯 **INTEGRAÇÃO FRONTEND**

### **docker-api.ts - Cliente API**
```typescript
import { apiClient } from '@/services/api/client';
import { Container, CreateContainerRequest, ContainerStats } from '@/types/containers';

export const dockerApi = {
  // Listar containers
  async getContainers(options: { all?: boolean } = {}): Promise<Container[]> {
    const { data } = await apiClient.get('/api/containers/docker', {
      params: options
    });
    return data;
  },

  // Obter container por ID
  async getContainer(id: string): Promise<Container> {
    const { data } = await apiClient.get(`/api/containers/docker/${id}`);
    return data;
  },

  // Criar container
  async createContainer(request: CreateContainerRequest): Promise<Container> {
    const { data } = await apiClient.post('/api/containers/docker', {
      ...request,
      engine: 'docker'
    });
    return data;
  },

  // Controle de ciclo de vida
  async startContainer(id: string): Promise<boolean> {
    const { data } = await apiClient.post(`/api/containers/docker/${id}/start`);
    return data.success;
  },

  async stopContainer(id: string): Promise<boolean> {
    const { data } = await apiClient.post(`/api/containers/docker/${id}/stop`);
    return data.success;
  },

  async restartContainer(id: string): Promise<boolean> {
    const { data } = await apiClient.post(`/api/containers/docker/${id}/restart`);
    return data.success;
  },

  async removeContainer(id: string, force = false): Promise<boolean> {
    const { data } = await apiClient.delete(`/api/containers/docker/${id}`, {
      params: { force }
    });
    return data.success;
  },

  // Estatísticas
  async getContainerStats(id: string): Promise<ContainerStats> {
    const { data } = await apiClient.get(`/api/containers/docker/${id}/stats`);
    return data;
  },

  // Logs
  async getContainerLogs(id: string, options: { tail?: number } = {}): Promise<string> {
    const { data } = await apiClient.get(`/api/containers/docker/${id}/logs`, {
      params: options
    });
    return data;
  },

  // Verificar status do Docker
  async getDockerInfo(): Promise<{
    isInstalled: boolean;
    isRunning: boolean;
    version?: string;
  }> {
    const { data } = await apiClient.get('/api/engines/docker/info');
    return data;
  }
};
```

### **docker-hooks.ts - Hooks React**
```typescript
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { dockerApi } from './docker-api';
import { useUIStore } from '@/store/ui';

export const useDockerContainers = (options: { all?: boolean } = {}) => {
  return useQuery({
    queryKey: ['docker-containers', options],
    queryFn: () => dockerApi.getContainers(options),
    refetchInterval: 5000,
    staleTime: 30000,
  });
};

export const useDockerContainer = (id: string) => {
  return useQuery({
    queryKey: ['docker-container', id],
    queryFn: () => dockerApi.getContainer(id),
    enabled: !!id,
  });
};

export const useDockerMutations = () => {
  const queryClient = useQueryClient();
  const { addNotification } = useUIStore();

  const createContainer = useMutation({
    mutationFn: dockerApi.createContainer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['docker-containers'] });
      addNotification({
        type: 'success',
        title: 'Container criado',
        message: 'Container Docker criado com sucesso'
      });
    },
    onError: (error) => {
      addNotification({
        type: 'error',
        title: 'Erro',
        message: `Falha ao criar container: ${error.message}`
      });
    }
  });

  const startContainer = useMutation({
    mutationFn: dockerApi.startContainer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['docker-containers'] });
    }
  });

  const stopContainer = useMutation({
    mutationFn: dockerApi.stopContainer,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['docker-containers'] });
    }
  });

  return {
    createContainer,
    startContainer,
    stopContainer,
  };
};

export const useDockerInfo = () => {
  return useQuery({
    queryKey: ['docker-info'],
    queryFn: dockerApi.getDockerInfo,
    staleTime: 60000, // 1 minuto
    refetchInterval: 30000, // 30 segundos
  });
};
```

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Integração Docker  
**✨ Status:** Documentação Completa**
