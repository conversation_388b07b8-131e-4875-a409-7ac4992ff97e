/**
 * Index - Ponto de entrada da aplicação React
 * Auto-Instalador V3 Lite
 * 
 * @description Arquivo principal que inicializa a aplicação React
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';
import { createRoot } from 'react-dom/client';
import { App } from './App';
import './styles/globals.css';

// Verificar se o elemento root existe
const container = document.getElementById('root');
if (!container) {
  throw new Error('Elemento root não encontrado. Verifique se existe um elemento com id="root" no HTML.');
}

// Criar root do React 18+
const root = createRoot(container);

// Renderizar aplicação
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// Hot Module Replacement para desenvolvimento
if (process.env.NODE_ENV === 'development' && module.hot) {
  module.hot.accept('./App', () => {
    const NextApp = require('./App').App;
    root.render(
      <React.StrictMode>
        <NextApp />
      </React.StrictMode>
    );
  });
}
