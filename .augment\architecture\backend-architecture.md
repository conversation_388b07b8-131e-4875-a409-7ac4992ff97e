# 🏗️ Arquitetura Backend - Auto-Instalador V3 Lite

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Documentar a arquitetura backend .NET 8.0  

---

## 🌟 **VISÃO GERAL BACKEND**

O backend do Auto-Instalador V3 Lite segue os princípios da **Clean Architecture** com .NET 8.0, proporcionando:
- **Separação de Responsabilidades:** Camadas bem definidas
- **Testabilidade:** Código facilmente testável
- **Manutenibilidade:** Estrutura organizada e extensível
- **Performance:** Otimizações específicas para desktop

### **Arquitetura em Camadas**
```
┌─────────────────────────────────────────────────────────────┐
│                        API LAYER                            │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   CONTROLLERS                           │ │
│  │  - ContainersController                                 │ │
│  │  - ContainerEnginesController                           │ │
│  │  - SettingsController                                   │ │
│  │  - HealthController                                     │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   MIDD<PERSON><PERSON>RE                            │ │
│  │  - <PERSON><PERSON><PERSON> Handling                                       │ │
│  │  - Request Logging                                      │ │
│  │  - CORS Configuration                                   │ │
│  │  - Rate Limiting                                        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   APPLICATION LAYER                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    SERVICES                             │ │
│  │  - ContainerService                                     │ │
│  │  - ContainerImageService                                │ │
│  │  - ContainerEngineService                               │ │
│  │  - SettingsService                                      │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   USE CASES                             │ │
│  │  - CreateContainerUseCase                               │ │
│  │  - ManageContainerLifecycleUseCase                      │ │
│  │  - MonitorContainerStatsUseCase                         │ │
│  │  - DetectContainerEnginesUseCase                        │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                     DTOs                                │ │
│  │  - Request DTOs                                         │ │
│  │  - Response DTOs                                        │ │
│  │  - Mapping Profiles                                     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 INFRASTRUCTURE LAYER                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │               EXTERNAL SERVICES                         │ │
│  │  - DockerService                                        │ │
│  │  - PodmanService                                        │ │
│  │  - ContainerEngineDetector                              │ │
│  │  - FileSystemService                                    │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 REPOSITORIES                            │ │
│  │  - ContainerRepository                                  │ │
│  │  - SettingsRepository                                   │ │
│  │  - CacheRepository                                      │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                DATA PERSISTENCE                         │ │
│  │  - Entity Framework DbContext                           │ │
│  │  - Redis Cache                                          │ │
│  │  - File System Access                                   │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      CORE LAYER                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    ENTITIES                             │ │
│  │  - Container                                            │ │
│  │  - ContainerImage                                       │ │
│  │  - ContainerEngineInfo                                  │ │
│  │  - ContainerStats                                       │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  INTERFACES                             │ │
│  │  - IContainerService                                    │ │
│  │  - IContainerRepository                                 │ │
│  │  - IContainerEngine                                     │ │
│  │  - IContainerEngineDetector                             │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 DOMAIN LOGIC                            │ │
│  │  - Business Rules                                       │ │
│  │  - Value Objects                                        │ │
│  │  - Domain Events                                        │ │
│  │  - Specifications                                       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 🎯 **CAMADA CORE (DOMÍNIO)**

### **Entidades Principais**
```csharp
// Container.cs - Entidade principal
public class Container
{
    public string Id { get; set; }
    public string Name { get; set; }
    public string ImageName { get; set; }
    public ContainerStatus Status { get; set; }
    public ContainerEngine Engine { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public Dictionary<string, string> Labels { get; set; }
    public List<ContainerPort> Ports { get; set; }
    public ContainerStats? Stats { get; set; }
    
    // Métodos de domínio
    public bool CanStart() => Status == ContainerStatus.Stopped;
    public bool CanStop() => Status == ContainerStatus.Running;
    public bool CanRestart() => Status != ContainerStatus.Removing;
}

// ContainerStats.cs - Estatísticas em tempo real
public class ContainerStats
{
    public string ContainerId { get; set; }
    public double CpuUsagePercent { get; set; }
    public long MemoryUsageBytes { get; set; }
    public long MemoryLimitBytes { get; set; }
    public double MemoryUsagePercent { get; set; }
    public long NetworkRxBytes { get; set; }
    public long NetworkTxBytes { get; set; }
    public DateTime Timestamp { get; set; }
}
```

### **Enums e Value Objects**
```csharp
// ContainerStatus.cs
public enum ContainerStatus
{
    Created,
    Running,
    Paused,
    Restarting,
    Removing,
    Stopped,
    Dead
}

// ContainerEngine.cs
public enum ContainerEngine
{
    Docker,
    Podman
}

// Platform.cs
public enum Platform
{
    Windows,
    Linux,
    MacOS
}
```

### **Interfaces de Serviços**
```csharp
// IContainerService.cs - Interface principal
public interface IContainerService
{
    Task<IEnumerable<Container>> GetAllAsync(bool includeAll = false);
    Task<Container?> GetByIdAsync(string id);
    Task<Container> CreateAsync(CreateContainerRequest request);
    Task<bool> StartAsync(string id);
    Task<bool> StopAsync(string id);
    Task<bool> RestartAsync(string id);
    Task<bool> RemoveAsync(string id, bool force = false);
    Task<ContainerStats?> GetStatsAsync(string id);
    Task<string> GetLogsAsync(string id, int tail = 100);
}

// IContainerEngineDetector.cs - Detecção de engines
public interface IContainerEngineDetector
{
    Task<IEnumerable<ContainerEngineInfo>> DetectAvailableEnginesAsync();
    Task<bool> IsEngineAvailableAsync(ContainerEngine engine);
    Task<ContainerEngineInfo?> GetEngineInfoAsync(ContainerEngine engine);
    Task<bool> InstallEngineAsync(ContainerEngine engine, Platform platform);
}
```

---

## 🔧 **CAMADA DE INFRAESTRUTURA**

### **Serviços de Container**
```csharp
// DockerService.cs - Implementação Docker
public class DockerService : IContainerEngine
{
    private readonly ILogger<DockerService> _logger;
    private readonly DockerClient _dockerClient;

    public ContainerEngine Engine => ContainerEngine.Docker;

    public async Task<IEnumerable<Container>> ListContainersAsync(bool all = false)
    {
        var containers = await _dockerClient.Containers.ListContainersAsync(
            new ContainersListParameters { All = all });
            
        return containers.Select(MapToContainer);
    }

    public async Task<bool> StartContainerAsync(string id)
    {
        try
        {
            var result = await _dockerClient.Containers.StartContainerAsync(
                id, new ContainerStartParameters());
            return result;
        }
        catch (DockerApiException ex)
        {
            _logger.LogError(ex, "Erro ao iniciar container {ContainerId}", id);
            return false;
        }
    }
}

// PodmanService.cs - Implementação Podman
public class PodmanService : IContainerEngine
{
    private readonly ILogger<PodmanService> _logger;
    private readonly HttpClient _httpClient;

    public ContainerEngine Engine => ContainerEngine.Podman;

    public async Task<IEnumerable<Container>> ListContainersAsync(bool all = false)
    {
        var response = await _httpClient.GetAsync($"/containers/json?all={all}");
        var json = await response.Content.ReadAsStringAsync();
        var containers = JsonSerializer.Deserialize<PodmanContainer[]>(json);
        
        return containers.Select(MapToContainer);
    }
}
```

### **Repositórios**
```csharp
// ContainerRepository.cs - Persistência de dados
public class ContainerRepository : IContainerRepository
{
    private readonly AppDbContext _context;
    private readonly IMemoryCache _cache;

    public async Task<Container?> GetByIdAsync(string id)
    {
        // Verifica cache primeiro
        if (_cache.TryGetValue($"container_{id}", out Container? cached))
            return cached;

        // Busca no banco
        var container = await _context.Containers
            .Include(c => c.Stats)
            .FirstOrDefaultAsync(c => c.Id == id);

        // Adiciona ao cache
        if (container != null)
        {
            _cache.Set($"container_{id}", container, TimeSpan.FromMinutes(5));
        }

        return container;
    }

    public async Task<IEnumerable<Container>> GetAllAsync()
    {
        return await _context.Containers
            .Include(c => c.Stats)
            .OrderBy(c => c.Name)
            .ToListAsync();
    }
}
```

### **Entity Framework DbContext**
```csharp
// AppDbContext.cs
public class AppDbContext : DbContext
{
    public DbSet<Container> Containers { get; set; }
    public DbSet<ContainerStats> ContainerStats { get; set; }
    public DbSet<Setting> Settings { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Configuração Container
        modelBuilder.Entity<Container>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.Property(e => e.Name).IsRequired().HasMaxLength(255);
            entity.Property(e => e.ImageName).IsRequired().HasMaxLength(500);
            entity.Property(e => e.Status).HasConversion<string>();
            entity.Property(e => e.Engine).HasConversion<string>();
            entity.Property(e => e.Labels).HasConversion(
                v => JsonSerializer.Serialize(v, (JsonSerializerOptions)null),
                v => JsonSerializer.Deserialize<Dictionary<string, string>>(v, (JsonSerializerOptions)null));
        });

        // Configuração ContainerStats
        modelBuilder.Entity<ContainerStats>(entity =>
        {
            entity.HasKey(e => new { e.ContainerId, e.Timestamp });
            entity.HasOne<Container>()
                  .WithMany()
                  .HasForeignKey(e => e.ContainerId);
        });
    }
}
```

---

## 🚀 **CAMADA DE APLICAÇÃO**

### **Serviços de Aplicação**
```csharp
// ContainerService.cs - Serviço principal
public class ContainerService : IContainerService
{
    private readonly IContainerRepository _repository;
    private readonly IContainerEngineManager _engineManager;
    private readonly IMapper _mapper;
    private readonly ILogger<ContainerService> _logger;

    public async Task<IEnumerable<Container>> GetAllAsync(bool includeAll = false)
    {
        // Busca containers de todos os engines disponíveis
        var engines = await _engineManager.GetAvailableEnginesAsync();
        var allContainers = new List<Container>();

        foreach (var engine in engines)
        {
            try
            {
                var containers = await engine.ListContainersAsync(includeAll);
                allContainers.AddRange(containers);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Erro ao listar containers do engine {Engine}", 
                    engine.Engine);
            }
        }

        return allContainers.OrderBy(c => c.Name);
    }

    public async Task<Container> CreateAsync(CreateContainerRequest request)
    {
        // Validação
        var validator = new CreateContainerRequestValidator();
        var validationResult = await validator.ValidateAsync(request);
        
        if (!validationResult.IsValid)
        {
            throw new ValidationException(validationResult.Errors);
        }

        // Seleciona engine
        var engine = await _engineManager.GetEngineAsync(request.Engine);
        if (engine == null)
        {
            throw new InvalidOperationException($"Engine {request.Engine} não disponível");
        }

        // Cria container
        var container = await engine.CreateContainerAsync(request);
        
        // Persiste no banco
        await _repository.AddAsync(container);
        
        _logger.LogInformation("Container {ContainerName} criado com sucesso", 
            container.Name);
            
        return container;
    }
}
```

### **DTOs e Mapeamento**
```csharp
// CreateContainerRequest.cs
public class CreateContainerRequest
{
    public string Name { get; set; } = string.Empty;
    public string ImageName { get; set; } = string.Empty;
    public ContainerEngine Engine { get; set; } = ContainerEngine.Docker;
    public Dictionary<string, string> Environment { get; set; } = new();
    public List<PortMapping> Ports { get; set; } = new();
    public List<VolumeMount> Volumes { get; set; } = new();
    public string? WorkingDirectory { get; set; }
    public string? Command { get; set; }
}

// ContainerResponse.cs
public class ContainerResponse
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string ImageName { get; set; } = string.Empty;
    public string Status { get; set; } = string.Empty;
    public string Engine { get; set; } = string.Empty;
    public DateTime CreatedAt { get; set; }
    public DateTime? StartedAt { get; set; }
    public ContainerStatsResponse? Stats { get; set; }
}

// AutoMapper Profile
public class ContainerMappingProfile : Profile
{
    public ContainerMappingProfile()
    {
        CreateMap<Container, ContainerResponse>()
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status.ToString()))
            .ForMember(dest => dest.Engine, opt => opt.MapFrom(src => src.Engine.ToString()));
            
        CreateMap<CreateContainerRequest, Container>()
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(_ => DateTime.UtcNow))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(_ => ContainerStatus.Created));
    }
}
```

---

## 🌐 **CAMADA DE API**

### **Controllers**
```csharp
// ContainersController.cs
[ApiController]
[Route("api/[controller]")]
public class ContainersController : ControllerBase
{
    private readonly IContainerService _containerService;
    private readonly IMapper _mapper;

    [HttpGet]
    public async Task<ActionResult<IEnumerable<ContainerResponse>>> GetAll(
        [FromQuery] bool all = false)
    {
        var containers = await _containerService.GetAllAsync(all);
        var response = _mapper.Map<IEnumerable<ContainerResponse>>(containers);
        return Ok(response);
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<ContainerResponse>> GetById(string id)
    {
        var container = await _containerService.GetByIdAsync(id);
        if (container == null)
            return NotFound();

        var response = _mapper.Map<ContainerResponse>(container);
        return Ok(response);
    }

    [HttpPost]
    public async Task<ActionResult<ContainerResponse>> Create(
        CreateContainerRequest request)
    {
        var container = await _containerService.CreateAsync(request);
        var response = _mapper.Map<ContainerResponse>(container);
        
        return CreatedAtAction(nameof(GetById), 
            new { id = container.Id }, response);
    }

    [HttpPost("{id}/start")]
    public async Task<ActionResult> Start(string id)
    {
        var result = await _containerService.StartAsync(id);
        return result ? Ok() : BadRequest("Não foi possível iniciar o container");
    }

    [HttpPost("{id}/stop")]
    public async Task<ActionResult> Stop(string id)
    {
        var result = await _containerService.StopAsync(id);
        return result ? Ok() : BadRequest("Não foi possível parar o container");
    }
}
```

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Arquitetura Backend  
**✨ Status:** Documentação Completa**
