# 🔄 Fluxo de Dados - Auto-Instalador V3 Lite

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Documentar o fluxo completo de dados no sistema  

---

## 🌊 **VISÃO GERAL DO FLUXO DE DADOS**

O Auto-Instalador V3 Lite implementa um fluxo de dados bidirecional que conecta:
- **Interface do Usuário (React)** ↔ **Estado da Aplicação (Zustand/React Query)**
- **Frontend (Electron Renderer)** ↔ **Backend (.NET API)** via HTTP
- **Frontend (Electron Renderer)** ↔ **Main Process (Electron)** via IPC
- **Backend (.NET)** ↔ **Engines de Container (Docker/Podman)** via APIs nativas
- **Backend (.NET)** ↔ **Banco de Dados (SQLite)** via Entity Framework

### **Diagrama de Fluxo Principal**
```
┌─────────────────────────────────────────────────────────────┐
│                    ELECTRON RENDERER                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  REACT UI LAYER                         │ │
│  │  User Action → Component → Event Handler               │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                             │
│                              ▼                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │               STATE MANAGEMENT                          │ │
│  │  ┌─────────────────┐    ┌─────────────────────────────┐ │ │
│  │  │   ZUSTAND       │    │      REACT QUERY            │ │ │
│  │  │ - UI State      │    │ - Server State              │ │ │
│  │  │ - Preferences   │    │ - Cache Management          │ │ │
│  │  │ - Notifications │    │ - Background Sync           │ │ │
│  │  └─────────────────┘    └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                             │
│                              ▼                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 SERVICE LAYER                           │ │
│  │  ┌─────────────────┐    ┌─────────────────────────────┐ │ │
│  │  │   HTTP CLIENT   │    │       IPC BRIDGE            │ │ │
│  │  │ - API Calls     │    │ - System Operations         │ │ │
│  │  │ - Error Handle  │    │ - File Access               │ │ │
│  │  │ - Interceptors  │    │ - Native Features           │ │ │
│  │  └─────────────────┘    └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │                    │
                              │ HTTP               │ IPC
                              ▼                    ▼
┌─────────────────────────────────────┐   ┌─────────────────────┐
│           .NET BACKEND              │   │   ELECTRON MAIN     │
│  ┌─────────────────────────────────┐│   │  ┌─────────────────┐│
│  │          API LAYER              ││   │  │  IPC HANDLERS   ││
│  │ - Controllers                   ││   │  │ - File System   ││
│  │ - Middleware                    ││   │  │ - System Info   ││
│  │ - Validation                    ││   │  │ - Notifications ││
│  └─────────────────────────────────┘│   │  └─────────────────┘│
│                 │                   │   └─────────────────────┘
│                 ▼                   │
│  ┌─────────────────────────────────┐│
│  │       APPLICATION LAYER         ││
│  │ - Services                      ││
│  │ - Use Cases                     ││
│  │ - Business Logic                ││
│  └─────────────────────────────────┘│
│                 │                   │
│                 ▼                   │
│  ┌─────────────────────────────────┐│
│  │     INFRASTRUCTURE LAYER        ││
│  │ - Container Services            ││
│  │ - External APIs                 ││
│  │ - Repositories                  ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
                 │
                 ▼
┌─────────────────────────────────────┐
│           DATA LAYER                │
│  ┌─────────────────────────────────┐│
│  │         SQLite DB               ││
│  │ - Containers                    ││
│  │ - Settings                      ││
│  │ - Logs                          ││
│  └─────────────────────────────────┘│
│  ┌─────────────────────────────────┐│
│  │        REDIS CACHE              ││
│  │ - Session Data                  ││
│  │ - Temporary Cache               ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
                 │
                 ▼
┌─────────────────────────────────────┐
│       CONTAINER ENGINES             │
│  ┌─────────────────────────────────┐│
│  │         DOCKER API              ││
│  │ - Container Operations          ││
│  │ - Image Management              ││
│  │ - Stats Collection              ││
│  └─────────────────────────────────┘│
│  ┌─────────────────────────────────┐│
│  │         PODMAN API              ││
│  │ - Container Operations          ││
│  │ - Image Management              ││
│  │ - Stats Collection              ││
│  └─────────────────────────────────┘│
└─────────────────────────────────────┘
```

---

## 🎯 **FLUXOS ESPECÍFICOS POR OPERAÇÃO**

### **1. Listar Containers**
```
User clicks "Refresh" button
         ↓
React Component (ContainerList)
         ↓
useContainers hook
         ↓
React Query (cache check)
         ↓
containerApi.getAll()
         ↓
HTTP GET /api/containers
         ↓
ContainersController.GetAll()
         ↓
IContainerService.GetAllAsync()
         ↓
ContainerManagerService (orchestrates engines)
         ↓
DockerService.ListContainersAsync() + PodmanService.ListContainersAsync()
         ↓
Docker API + Podman API (parallel calls)
         ↓
Merge results and map to domain entities
         ↓
Return to frontend via HTTP response
         ↓
React Query updates cache
         ↓
Component re-renders with new data
```

### **2. Iniciar Container**
```
User clicks "Start" button
         ↓
ContainerControls component
         ↓
handleContainerAction('start')
         ↓
useContainerMutations.startContainer
         ↓
containerApi.start(containerId)
         ↓
HTTP POST /api/containers/{id}/start
         ↓
ContainersController.Start()
         ↓
IContainerService.StartAsync()
         ↓
Determine container engine (Docker/Podman)
         ↓
DockerService.StartContainerAsync() OR PodmanService.StartContainerAsync()
         ↓
Engine-specific API call
         ↓
Update container status in database
         ↓
Return success/failure to frontend
         ↓
React Query invalidates cache
         ↓
UI updates with new container status
         ↓
Show success/error notification
```

### **3. Monitoramento de Estatísticas**
```
Component mounts (ContainerStats)
         ↓
useQuery with refetchInterval: 2000ms
         ↓
containerApi.getStats(containerId)
         ↓
HTTP GET /api/containers/{id}/stats
         ↓
ContainersController.GetStats()
         ↓
IContainerService.GetStatsAsync()
         ↓
Determine container engine
         ↓
DockerService.GetContainerStatsAsync() OR PodmanService.GetContainerStatsAsync()
         ↓
Engine-specific stats API call
         ↓
Map raw stats to ContainerStats entity
         ↓
Return formatted stats to frontend
         ↓
React Query caches stats data
         ↓
Component renders charts and metrics
         ↓
Process repeats every 2 seconds
```

---

## 🔄 **FLUXOS DE COMUNICAÇÃO IPC**

### **Acesso ao Sistema de Arquivos**
```
React Component needs file path
         ↓
window.electronAPI.files.selectDirectory()
         ↓
IPC call to main process
         ↓
Main process shows native dialog
         ↓
User selects directory
         ↓
Main process returns path via IPC
         ↓
Renderer receives path
         ↓
Component updates state
```

### **Notificações do Sistema**
```
Backend operation completes
         ↓
Frontend receives success response
         ↓
window.electronAPI.notifications.show()
         ↓
IPC call to main process
         ↓
Main process shows native notification
         ↓
User sees system notification
```

---

## 📊 **GERENCIAMENTO DE ESTADO**

### **Estado do Servidor (React Query)**
```typescript
// Fluxo de cache do React Query
Query Key: ['containers', { all: true }]
         ↓
Cache Check (staleTime: 30s)
         ↓
If stale or missing:
  - Fetch from API
  - Update cache
  - Notify components
         ↓
If fresh:
  - Return cached data
  - Background refetch if configured
         ↓
Components receive data
         ↓
Automatic re-render
```

### **Estado da UI (Zustand)**
```typescript
// Fluxo de estado local
User action (toggle sidebar)
         ↓
Component calls useUIStore.toggleSidebar()
         ↓
Zustand updates state immutably
         ↓
All subscribed components re-render
         ↓
UI reflects new state
         ↓
State persisted to localStorage (if configured)
```

---

## 🔐 **FLUXOS DE SEGURANÇA**

### **Validação de Entrada**
```
User submits form
         ↓
Frontend validation (React Hook Form + Zod)
         ↓
If valid: HTTP request to backend
         ↓
Backend validation (FluentValidation)
         ↓
If valid: Process request
         ↓
If invalid: Return validation errors
         ↓
Frontend displays errors to user
```

### **Tratamento de Erros**
```
API call fails
         ↓
HTTP interceptor catches error
         ↓
Error mapped to user-friendly message
         ↓
React Query error state updated
         ↓
Component shows error UI
         ↓
User notification displayed
         ↓
Retry mechanism available
```

---

## 📈 **OTIMIZAÇÕES DE PERFORMANCE**

### **Cache Strategy**
```
Data Request
         ↓
React Query Cache Check
         ↓
If cached and fresh: Return immediately
         ↓
If stale: Return cached + background fetch
         ↓
If missing: Show loading + fetch
         ↓
Redis Cache Check (backend)
         ↓
If cached: Return from Redis
         ↓
If missing: Fetch from source + cache in Redis
         ↓
Return to frontend
```

### **Debouncing e Throttling**
```
User types in search box
         ↓
Input debounced (300ms)
         ↓
Search query triggered
         ↓
Previous request cancelled
         ↓
New request sent
         ↓
Results displayed
```

---

## 🔄 **FLUXOS DE SINCRONIZAÇÃO**

### **Real-time Updates**
```
Container status changes externally
         ↓
Backend polling detects change (5s interval)
         ↓
Database updated
         ↓
Frontend refetch interval (5s) picks up change
         ↓
React Query updates cache
         ↓
Components re-render with new data
         ↓
User sees updated status
```

### **Optimistic Updates**
```
User clicks "Start Container"
         ↓
UI immediately shows "Starting..." status
         ↓
API request sent in background
         ↓
If successful: UI already correct
         ↓
If failed: Revert UI + show error
         ↓
User gets immediate feedback
```

---

## 📝 **LOGGING E AUDITORIA**

### **Fluxo de Logs**
```
Operation occurs (any layer)
         ↓
Structured log entry created (Serilog)
         ↓
Log written to file + console
         ↓
Important events also stored in database
         ↓
Frontend can query logs via API
         ↓
Logs displayed in admin interface
```

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Fluxo de Dados  
**✨ Status:** Documentação Completa do Fluxo**
