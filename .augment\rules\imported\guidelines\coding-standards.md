---
type: "agent_requested"
description: "Example description"
---
# 📝 Padrões de Codificação - Auto-Instalador V3 Lite

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Estabelecer padrões de código consistentes  

---

## 🌟 **PRINCÍPIOS FUNDAMENTAIS**

### **Princípios SOLID**
- **S** - Single Responsibility Principle (Responsabilidade Única)
- **O** - Open/Closed Principle (Aberto/Fechado)
- **L** - Liskov Substitution Principle (Substituição de Liskov)
- **I** - Interface Segregation Principle (Segregação de Interface)
- **D** - Dependency Inversion Principle (Inversão de Dependência)

### **Princípios DRY e KISS**
- **DRY** - Don't Repeat Yourself (Não se Repita)
- **KISS** - Keep It Simple, Stupid (Mantenha Simples)
- **YAGNI** - You Aren't Gonna Need It (Você Não Vai Precisar Disso)

---

## 🎯 **PADRÕES TYPESCRIPT/REACT**

### **Nomenclatura**
```typescript
// ✅ CORRETO - PascalCase para componentes
const ContainerDashboard: React.FC = () => {
  return <div>Dashboard</div>;
};

// ✅ CORRETO - camelCase para variáveis e funções
const containerCount = 5;
const handleContainerStart = () => {};

// ✅ CORRETO - SCREAMING_SNAKE_CASE para constantes
const MAX_CONTAINER_NAME_LENGTH = 255;
const API_ENDPOINTS = {
  CONTAINERS: '/api/containers',
  IMAGES: '/api/images'
} as const;

// ✅ CORRETO - kebab-case para arquivos
// container-dashboard.tsx
// container-list.component.tsx
// use-containers.hook.ts
```

### **Estrutura de Componentes**
```typescript
// ✅ PADRÃO RECOMENDADO - Estrutura de componente
import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { useContainers } from '@/hooks/use-containers';
import { Container } from '@/types/containers';
import { Button } from '@/components/ui/button';

// Props interface sempre tipada
interface ContainerListProps {
  showAll?: boolean;
  onContainerSelect?: (container: Container) => void;
  className?: string;
}

// Componente funcional com tipagem explícita
export const ContainerList: React.FC<ContainerListProps> = ({
  showAll = false,
  onContainerSelect,
  className = ''
}) => {
  // Estados locais
  const [selectedId, setSelectedId] = useState<string | null>(null);
  
  // Hooks customizados
  const { data: containers, isLoading, error } = useContainers({ all: showAll });
  
  // Callbacks memoizados
  const handleSelect = useCallback((container: Container) => {
    setSelectedId(container.id);
    onContainerSelect?.(container);
  }, [onContainerSelect]);
  
  // Effects
  useEffect(() => {
    if (error) {
      console.error('Erro ao carregar containers:', error);
    }
  }, [error]);
  
  // Early returns para loading/error
  if (isLoading) {
    return <div className="animate-pulse">Carregando containers...</div>;
  }
  
  if (error) {
    return <div className="text-red-500">Erro ao carregar containers</div>;
  }
  
  // Render principal
  return (
    <motion.div 
      className={`container-list ${className}`}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.2 }}
    >
      {containers?.map((container) => (
        <ContainerItem
          key={container.id}
          container={container}
          isSelected={selectedId === container.id}
          onSelect={handleSelect}
        />
      ))}
    </motion.div>
  );
};

// Export default apenas se for o componente principal do arquivo
export default ContainerList;
```

### **Hooks Customizados**
```typescript
// ✅ PADRÃO RECOMENDADO - Hook customizado
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { containerApi } from '@/services/api/containers';
import { Container, CreateContainerRequest } from '@/types/containers';

// Interface para opções do hook
interface UseContainersOptions {
  all?: boolean;
  filters?: {
    status?: string[];
    engine?: string;
  };
  refetchInterval?: number;
}

// Hook principal
export const useContainers = (options: UseContainersOptions = {}) => {
  const { all = false, filters, refetchInterval = 5000 } = options;
  
  return useQuery({
    queryKey: ['containers', { all, filters }],
    queryFn: () => containerApi.getAll({ all, filters }),
    refetchInterval,
    staleTime: 30000, // 30 segundos
    cacheTime: 300000, // 5 minutos
  });
};

// Hook para mutations
export const useContainerMutations = () => {
  const queryClient = useQueryClient();
  
  const createContainer = useMutation({
    mutationFn: (request: CreateContainerRequest) => 
      containerApi.create(request),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['containers'] });
    },
  });
  
  const startContainer = useMutation({
    mutationFn: (id: string) => containerApi.start(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['containers'] });
    },
  });
  
  return {
    createContainer,
    startContainer,
  };
};
```

### **Tipagem TypeScript**
```typescript
// ✅ PADRÃO RECOMENDADO - Definição de tipos
// types/containers.ts

// Enum para status
export enum ContainerStatus {
  CREATED = 'created',
  RUNNING = 'running',
  PAUSED = 'paused',
  RESTARTING = 'restarting',
  REMOVING = 'removing',
  STOPPED = 'stopped',
  DEAD = 'dead'
}

// Interface base
export interface Container {
  readonly id: string;
  name: string;
  imageName: string;
  status: ContainerStatus;
  engine: 'docker' | 'podman';
  createdAt: Date;
  startedAt?: Date;
  labels: Record<string, string>;
  ports: PortMapping[];
  stats?: ContainerStats;
}

// Tipos derivados
export type ContainerSummary = Pick<Container, 'id' | 'name' | 'status' | 'engine'>;
export type CreateContainerData = Omit<Container, 'id' | 'createdAt' | 'startedAt' | 'stats'>;

// Union types para ações
export type ContainerAction = 'start' | 'stop' | 'restart' | 'pause' | 'remove';

// Utility types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

// Generic para listas paginadas
export interface PaginatedResponse<T> {
  items: T[];
  totalCount: number;
  pageSize: number;
  currentPage: number;
  totalPages: number;
}
```

---

## 🏗️ **PADRÕES C# / .NET**

### **Nomenclatura**
```csharp
// ✅ CORRETO - PascalCase para classes, métodos, propriedades
public class ContainerService : IContainerService
{
    public async Task<Container> GetByIdAsync(string id) { }
    public string ContainerName { get; set; }
}

// ✅ CORRETO - camelCase para parâmetros e variáveis locais
public async Task<bool> StartContainerAsync(string containerId)
{
    var containerEngine = await GetEngineAsync();
    var isRunning = await containerEngine.IsRunningAsync(containerId);
    return isRunning;
}

// ✅ CORRETO - PascalCase para constantes
public const int MaxContainerNameLength = 255;
public static readonly TimeSpan DefaultTimeout = TimeSpan.FromSeconds(30);

// ✅ CORRETO - Interface com prefixo I
public interface IContainerRepository
{
    Task<Container?> GetByIdAsync(string id);
    Task<IEnumerable<Container>> GetAllAsync();
}
```

### **Estrutura de Classes**
```csharp
// ✅ PADRÃO RECOMENDADO - Estrutura de classe
using Microsoft.Extensions.Logging;
using AutoInstalador.Core.Entities;
using AutoInstalador.Core.Interfaces;

namespace AutoInstalador.Application.Services;

/// <summary>
/// Serviço responsável pelo gerenciamento de containers
/// </summary>
public class ContainerService : IContainerService
{
    // Campos privados readonly
    private readonly IContainerRepository _repository;
    private readonly IContainerEngineManager _engineManager;
    private readonly ILogger<ContainerService> _logger;
    
    // Construtor com injeção de dependência
    public ContainerService(
        IContainerRepository repository,
        IContainerEngineManager engineManager,
        ILogger<ContainerService> logger)
    {
        _repository = repository ?? throw new ArgumentNullException(nameof(repository));
        _engineManager = engineManager ?? throw new ArgumentNullException(nameof(engineManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
    
    // Métodos públicos
    public async Task<Container?> GetByIdAsync(string id)
    {
        // Validação de entrada
        if (string.IsNullOrWhiteSpace(id))
        {
            throw new ArgumentException("ID do container não pode ser vazio", nameof(id));
        }
        
        try
        {
            _logger.LogDebug("Buscando container com ID: {ContainerId}", id);
            
            var container = await _repository.GetByIdAsync(id);
            
            if (container == null)
            {
                _logger.LogWarning("Container não encontrado: {ContainerId}", id);
            }
            
            return container;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro ao buscar container {ContainerId}", id);
            throw;
        }
    }
    
    // Métodos privados auxiliares
    private async Task<bool> ValidateContainerAsync(Container container)
    {
        // Lógica de validação
        return await Task.FromResult(true);
    }
}
```

### **Tratamento de Erros**
```csharp
// ✅ PADRÃO RECOMENDADO - Tratamento de erros
public class ContainerController : ControllerBase
{
    [HttpGet("{id}")]
    public async Task<ActionResult<ContainerResponse>> GetById(string id)
    {
        try
        {
            var container = await _containerService.GetByIdAsync(id);
            
            if (container == null)
            {
                return NotFound($"Container com ID '{id}' não encontrado");
            }
            
            var response = _mapper.Map<ContainerResponse>(container);
            return Ok(response);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Parâmetro inválido: {Parameter}", nameof(id));
            return BadRequest(ex.Message);
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger.LogWarning(ex, "Acesso negado ao container {ContainerId}", id);
            return Forbid();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erro interno ao buscar container {ContainerId}", id);
            return StatusCode(500, "Erro interno do servidor");
        }
    }
}

// ✅ PADRÃO RECOMENDADO - Exceções customizadas
public class ContainerNotFoundException : Exception
{
    public string ContainerId { get; }
    
    public ContainerNotFoundException(string containerId) 
        : base($"Container '{containerId}' não foi encontrado")
    {
        ContainerId = containerId;
    }
    
    public ContainerNotFoundException(string containerId, Exception innerException) 
        : base($"Container '{containerId}' não foi encontrado", innerException)
    {
        ContainerId = containerId;
    }
}
```

---

## 🧪 **PADRÕES DE TESTE**

### **Testes Unitários - Frontend**
```typescript
// ✅ PADRÃO RECOMENDADO - Teste de componente
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi } from 'vitest';
import { ContainerList } from '@/components/features/containers/ContainerList';
import { containerApi } from '@/services/api/containers';

// Mock do serviço
vi.mock('@/services/api/containers');

describe('ContainerList', () => {
  let queryClient: QueryClient;
  
  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    });
  });
  
  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };
  
  it('deve exibir lista de containers', async () => {
    // Arrange
    const mockContainers = [
      { id: '1', name: 'container-1', status: 'running' },
      { id: '2', name: 'container-2', status: 'stopped' },
    ];
    
    vi.mocked(containerApi.getAll).mockResolvedValue(mockContainers);
    
    // Act
    renderWithProviders(<ContainerList />);
    
    // Assert
    await waitFor(() => {
      expect(screen.getByText('container-1')).toBeInTheDocument();
      expect(screen.getByText('container-2')).toBeInTheDocument();
    });
  });
  
  it('deve chamar callback ao selecionar container', async () => {
    // Arrange
    const mockOnSelect = vi.fn();
    const mockContainers = [
      { id: '1', name: 'container-1', status: 'running' },
    ];
    
    vi.mocked(containerApi.getAll).mockResolvedValue(mockContainers);
    
    // Act
    renderWithProviders(
      <ContainerList onContainerSelect={mockOnSelect} />
    );
    
    await waitFor(() => {
      fireEvent.click(screen.getByText('container-1'));
    });
    
    // Assert
    expect(mockOnSelect).toHaveBeenCalledWith(mockContainers[0]);
  });
});
```

### **Testes Unitários - Backend**
```csharp
// ✅ PADRÃO RECOMENDADO - Teste de serviço
using Xunit;
using Moq;
using Microsoft.Extensions.Logging;
using AutoInstalador.Application.Services;
using AutoInstalador.Core.Interfaces;
using AutoInstalador.Core.Entities;

public class ContainerServiceTests
{
    private readonly Mock<IContainerRepository> _repositoryMock;
    private readonly Mock<IContainerEngineManager> _engineManagerMock;
    private readonly Mock<ILogger<ContainerService>> _loggerMock;
    private readonly ContainerService _service;
    
    public ContainerServiceTests()
    {
        _repositoryMock = new Mock<IContainerRepository>();
        _engineManagerMock = new Mock<IContainerEngineManager>();
        _loggerMock = new Mock<ILogger<ContainerService>>();
        
        _service = new ContainerService(
            _repositoryMock.Object,
            _engineManagerMock.Object,
            _loggerMock.Object);
    }
    
    [Fact]
    public async Task GetByIdAsync_ContainerExiste_DeveRetornarContainer()
    {
        // Arrange
        var containerId = "test-container-id";
        var expectedContainer = new Container 
        { 
            Id = containerId, 
            Name = "test-container" 
        };
        
        _repositoryMock
            .Setup(r => r.GetByIdAsync(containerId))
            .ReturnsAsync(expectedContainer);
        
        // Act
        var result = await _service.GetByIdAsync(containerId);
        
        // Assert
        Assert.NotNull(result);
        Assert.Equal(containerId, result.Id);
        Assert.Equal("test-container", result.Name);
        
        _repositoryMock.Verify(r => r.GetByIdAsync(containerId), Times.Once);
    }
    
    [Theory]
    [InlineData("")]
    [InlineData(" ")]
    [InlineData(null)]
    public async Task GetByIdAsync_IdInvalido_DeveLancarArgumentException(string invalidId)
    {
        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(
            () => _service.GetByIdAsync(invalidId));
    }
}
```

---

## 📁 **ORGANIZAÇÃO DE ARQUIVOS**

### **Estrutura de Pastas Frontend**
```
src/frontend/renderer/
├── components/
│   ├── ui/                     # Componentes base reutilizáveis
│   ├── layout/                 # Componentes de layout
│   ├── features/               # Componentes específicos de features
│   └── forms/                  # Componentes de formulário
├── hooks/                      # Custom hooks
├── services/                   # Serviços e APIs
├── store/                      # Estado global
├── types/                      # Definições de tipos
├── utils/                      # Funções utilitárias
└── __tests__/                  # Testes organizados por feature
```

### **Estrutura de Pastas Backend**
```
src/backend/src/
├── AutoInstalador.API/         # Camada de apresentação
├── AutoInstalador.Application/ # Camada de aplicação
├── AutoInstalador.Infrastructure/ # Camada de infraestrutura
├── AutoInstalador.Core/        # Camada de domínio
└── AutoInstalador.Shared/      # Recursos compartilhados
```

---

## 🔧 **CONFIGURAÇÕES DE FERRAMENTAS**

### **ESLint Configuration**
```json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off",
    "prefer-const": "error",
    "no-var": "error"
  }
}
```

### **Prettier Configuration**
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Padrões de Codificação  
**✨ Status:** Diretrizes Estabelecidas**
