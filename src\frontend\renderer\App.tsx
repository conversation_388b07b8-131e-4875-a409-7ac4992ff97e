/**
 * App - Componente principal da aplicação
 * Auto-Instalador V3 Lite
 * 
 * @description Componente raiz que configura providers e roteamento
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { Toaster } from 'react-hot-toast';
import { AppRoutes } from './routes';
import { Layout } from './components/layout/Layout';

// Configuração do React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutos
      cacheTime: 10 * 60 * 1000, // 10 minutos
      retry: 2,
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
      // Configurações específicas para Electron
      networkMode: 'always'
    },
    mutations: {
      retry: 1,
      networkMode: 'always'
    }
  }
});

/**
 * Componente principal da aplicação
 */
export const App: React.FC = () => {
  // Handler para erros não capturados
  React.useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('Erro não capturado:', event.error);
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Promise rejeitada:', event.reason);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <Layout>
          <AppRoutes />
        </Layout>
        
        {/* Notificações Toast */}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: '#374151',
              color: '#fff',
              border: '1px solid #4B5563',
              borderRadius: '8px',
              fontSize: '14px'
            },
            success: {
              style: {
                background: '#065F46',
                border: '1px solid #10B981'
              },
              iconTheme: {
                primary: '#10B981',
                secondary: '#fff'
              }
            },
            error: {
              style: {
                background: '#7F1D1D',
                border: '1px solid #EF4444'
              },
              iconTheme: {
                primary: '#EF4444',
                secondary: '#fff'
              }
            },
            loading: {
              style: {
                background: '#1E3A8A',
                border: '1px solid #3B82F6'
              }
            }
          }}
        />
        
        {/* React Query DevTools - apenas em desenvolvimento */}
        {process.env.NODE_ENV === 'development' && (
          <ReactQueryDevtools 
            initialIsOpen={false}
            position="bottom-right"
            toggleButtonProps={{
              style: {
                background: '#374151',
                border: '1px solid #4B5563',
                color: '#fff'
              }
            }}
          />
        )}
      </BrowserRouter>
    </QueryClientProvider>
  );
};
