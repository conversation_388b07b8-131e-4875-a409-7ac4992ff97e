/**
 * Global Styles - Estilos globais da aplicação
 * Auto-Instalador V3 Lite
 * 
 * @description Estilos globais usando Tailwind CSS
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* ============================================================================
   BASE STYLES
   ============================================================================ */

@layer base {
  /* Reset e configurações básicas */
  * {
    box-sizing: border-box;
  }

  html {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', 
                 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Open Sans', 
                 'Helvetica Neue', sans-serif;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background-color: #111827; /* gray-900 */
    color: #ffffff;
    overflow: hidden; /* Para aplicação Electron */
  }

  #root {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
  }

  /* Scrollbars customizadas */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #374151; /* gray-700 */
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #6B7280; /* gray-500 */
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #9CA3AF; /* gray-400 */
  }

  /* Seleção de texto */
  ::selection {
    background-color: #3B82F6; /* blue-500 */
    color: #ffffff;
  }

  ::-moz-selection {
    background-color: #3B82F6; /* blue-500 */
    color: #ffffff;
  }
}

/* ============================================================================
   COMPONENT STYLES
   ============================================================================ */

@layer components {
  /* Botões base */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900;
  }

  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }

  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }

  .btn-success {
    @apply btn bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
  }

  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }

  .btn-warning {
    @apply btn bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
  }

  /* Inputs base */
  .input {
    @apply block w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200;
  }

  /* Cards */
  .card {
    @apply bg-gray-800 border border-gray-700 rounded-lg p-6 shadow-lg;
  }

  .card-hover {
    @apply card hover:border-blue-500 hover:shadow-blue-500/15 transition-all duration-200;
  }

  /* Status indicators */
  .status-running {
    @apply text-green-400 bg-green-400/10;
  }

  .status-stopped {
    @apply text-red-400 bg-red-400/10;
  }

  .status-paused {
    @apply text-yellow-400 bg-yellow-400/10;
  }

  .status-restarting {
    @apply text-blue-400 bg-blue-400/10;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-gray-700 rounded;
  }

  /* Tooltip */
  .tooltip {
    @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 border border-gray-600 rounded shadow-lg;
  }
}

/* ============================================================================
   UTILITY STYLES
   ============================================================================ */

@layer utilities {
  /* Truncate text with ellipsis */
  .truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Glassmorphism effect */
  .glass {
    backdrop-filter: blur(10px);
    background: rgba(55, 65, 81, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.3);
  }

  /* Focus visible only for keyboard navigation */
  .focus-visible-only:focus:not(:focus-visible) {
    outline: none;
    box-shadow: none;
  }

  /* Container queries support */
  .container-sm {
    container-type: inline-size;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
}

/* ============================================================================
   ANIMATIONS
   ============================================================================ */

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* ============================================================================
   RESPONSIVE DESIGN
   ============================================================================ */

/* Electron window specific styles */
@media (max-width: 1200px) {
  .container-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .sidebar-mobile {
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }
}

/* ============================================================================
   PRINT STYLES
   ============================================================================ */

@media print {
  .no-print {
    display: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
