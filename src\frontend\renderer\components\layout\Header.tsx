/**
 * Header - Cabeçal<PERSON> da aplicação
 * Auto-Instalador V3 Lite
 * 
 * @description Header com breadcrumb, ações e controles
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';
import { useLocation } from 'react-router-dom';
import { getBreadcrumb, generatePageTitle } from '../../config/navigation';

interface HeaderProps {
  showSidebarToggle: boolean;
  sidebarOpen: boolean;
  onSidebarToggle: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  showSidebarToggle,
  sidebarOpen,
  onSidebarToggle
}) => {
  const location = useLocation();
  const breadcrumb = getBreadcrumb(location.pathname.split('/').pop() || 'home');
  const pageTitle = generatePageTitle(location.pathname);

  // Atualizar título da janela
  React.useEffect(() => {
    document.title = pageTitle;
  }, [pageTitle]);

  return (
    <header className="bg-gray-700 border-b border-gray-600 px-6 py-4">
      <div className="flex items-center justify-between">
        {/* Left side */}
        <div className="flex items-center gap-4">
          {/* Sidebar toggle */}
          {showSidebarToggle && (
            <button
              type="button"
              onClick={onSidebarToggle}
              className="p-2 text-gray-400 hover:text-white hover:bg-gray-600 rounded-md transition-colors"
              title={sidebarOpen ? 'Recolher sidebar' : 'Expandir sidebar'}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          )}

          {/* Breadcrumb */}
          <nav className="flex items-center space-x-2 text-sm">
            {breadcrumb.map((item, index) => (
              <React.Fragment key={item.id}>
                {index > 0 && (
                  <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                )}
                <span className={`
                  ${index === breadcrumb.length - 1 
                    ? 'text-white font-medium' 
                    : 'text-gray-400 hover:text-white'
                  }
                `}>
                  {item.label}
                </span>
              </React.Fragment>
            ))}
          </nav>
        </div>

        {/* Right side */}
        <div className="flex items-center gap-3">
          {/* Status indicator */}
          <div className="flex items-center gap-2 px-3 py-1 bg-gray-800 rounded-md">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span className="text-xs text-gray-300 font-medium">
              Sistema Ativo
            </span>
          </div>

          {/* Refresh button */}
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-600 rounded-md transition-colors"
            title="Atualizar página"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>

          {/* Settings button */}
          <button
            type="button"
            onClick={() => {
              // TODO: Implementar modal de configurações rápidas
              console.log('Configurações rápidas');
            }}
            className="p-2 text-gray-400 hover:text-white hover:bg-gray-600 rounded-md transition-colors"
            title="Configurações rápidas"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>

          {/* Window controls (Electron) */}
          <div className="flex items-center gap-1 ml-2">
            <button
              type="button"
              onClick={() => {
                // TODO: Implementar minimize
                if (window.electronAPI) {
                  window.electronAPI.minimize();
                }
              }}
              className="w-3 h-3 bg-yellow-500 rounded-full hover:bg-yellow-400 transition-colors"
              title="Minimizar"
            />
            <button
              type="button"
              onClick={() => {
                // TODO: Implementar maximize/restore
                if (window.electronAPI) {
                  window.electronAPI.toggleMaximize();
                }
              }}
              className="w-3 h-3 bg-green-500 rounded-full hover:bg-green-400 transition-colors"
              title="Maximizar/Restaurar"
            />
            <button
              type="button"
              onClick={() => {
                // TODO: Implementar close
                if (window.electronAPI) {
                  window.electronAPI.close();
                }
              }}
              className="w-3 h-3 bg-red-500 rounded-full hover:bg-red-400 transition-colors"
              title="Fechar"
            />
          </div>
        </div>
      </div>
    </header>
  );
};
