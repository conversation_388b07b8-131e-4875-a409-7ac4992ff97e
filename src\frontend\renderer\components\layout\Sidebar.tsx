/**
 * Sidebar - Navegação lateral da aplicação
 * Auto-Instalador V3 Lite
 * 
 * @description Sidebar com navegação principal e seções
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import { navigationItems, isItemActive } from '../../config/navigation';

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, onToggle }) => {
  const location = useLocation();

  const renderNavigationItem = (item: typeof navigationItems[0], level = 0) => {
    const active = isItemActive(item, location.pathname);
    const hasChildren = item.children && item.children.length > 0;
    
    return (
      <div key={item.id} className={`${level > 0 ? 'ml-4' : ''}`}>
        <Link
          to={item.path}
          className={`
            flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
            ${active 
              ? 'bg-blue-900 text-blue-400 border-l-3 border-blue-400' 
              : 'text-gray-300 hover:bg-gray-700 hover:text-white'
            }
            ${!isOpen && level === 0 ? 'justify-center' : ''}
            ${item.disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
          title={!isOpen ? item.label : undefined}
        >
          {/* Ícone */}
          <item.icon className={`
            ${isOpen ? 'w-5 h-5' : 'w-6 h-6'}
            flex-shrink-0
          `} />
          
          {/* Label e Badge */}
          {isOpen && (
            <>
              <span className="flex-1 truncate">{item.label}</span>
              
              {/* Badge */}
              {item.badge && (
                <span className="px-2 py-0.5 text-xs bg-blue-600 text-white rounded-full">
                  {item.badge}
                </span>
              )}
              
              {/* Beta indicator */}
              {item.beta && (
                <span className="px-1.5 py-0.5 text-xs bg-yellow-600 text-white rounded">
                  β
                </span>
              )}
            </>
          )}
        </Link>

        {/* Children */}
        {hasChildren && isOpen && active && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavigationItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <aside className={`
      h-full bg-gray-700 border-r border-gray-600 flex flex-col
      transition-all duration-300 ease-in-out
      ${isOpen ? 'w-64' : 'w-16'}
    `}>
      {/* Header */}
      <div className="p-4 border-b border-gray-600">
        <div className="flex items-center gap-3">
          {/* Logo */}
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
            <span className="text-white font-bold text-sm">AI</span>
          </div>
          
          {/* Title */}
          {isOpen && (
            <div className="flex-1 min-w-0">
              <h1 className="text-white font-semibold text-sm truncate">
                Auto-Instalador
              </h1>
              <p className="text-gray-400 text-xs truncate">
                V3 Lite
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto p-4">
        <div className="space-y-2">
          {navigationItems.map(item => renderNavigationItem(item))}
        </div>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-gray-600">
        {isOpen ? (
          <div className="text-xs text-gray-400 text-center">
            <div>Versão 3.0.0-lite</div>
            <div>Build 2025.08.05</div>
          </div>
        ) : (
          <div className="flex justify-center">
            <div className="w-2 h-2 bg-green-500 rounded-full" title="Sistema ativo" />
          </div>
        )}
      </div>
    </aside>
  );
};
