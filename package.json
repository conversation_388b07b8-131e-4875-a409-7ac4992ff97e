{"name": "auto-instalador-v3-lite", "version": "3.0.0-lite", "description": "Auto-Instalador V3 Lite - Gerenciador de containers e pacotes", "main": "dist/electron/main/main.js", "homepage": "./", "author": "Augment Agent", "license": "MIT", "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:electron\"", "dev:renderer": "vite", "dev:electron": "wait-on http://localhost:3000 && electron .", "build": "npm run build:renderer && npm run build:electron", "build:renderer": "vite build", "build:electron": "tsc -p tsconfig.electron.json", "build:production": "npm run build && electron-builder", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "clean": "rimraf dist node_modules/.vite", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "@tanstack/react-query": "^4.35.0", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "electron": "^25.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.5.0", "@vitejs/plugin-react": "^4.0.4", "vite": "^4.4.9", "typescript": "^5.2.2", "tailwindcss": "^3.3.3", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "electron-builder": "^24.6.3", "concurrently": "^8.2.0", "wait-on": "^7.0.1", "vitest": "^0.34.3", "@vitest/ui": "^0.34.3", "c8": "^8.0.1", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.2", "@testing-library/user-event": "^14.4.3", "eslint": "^8.47.0", "@typescript-eslint/eslint-plugin": "^6.4.1", "@typescript-eslint/parser": "^6.4.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "rimraf": "^5.0.1"}, "build": {"appId": "com.augment.auto-instalador-v3-lite", "productName": "Auto-Instalador V3 Lite", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*", "package.json"], "extraResources": [{"from": "src/backend/bin", "to": "backend", "filter": ["**/*"]}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns", "category": "public.app-category.developer-tools"}, "linux": {"target": "AppImage", "icon": "assets/icon.png", "category": "Development"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "keywords": ["electron", "react", "typescript", "docker", "podman", "containers", "package-manager", "auto-installer"]}