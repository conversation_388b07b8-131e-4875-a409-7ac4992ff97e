# 🧪 Guia de Teste de Integração - Interface de Containers

## 🎯 **Objetivo**
Validar que a integração da interface de containers está funcionando corretamente com o projeto existente.

---

## 📋 **Checklist de Integração**

### **✅ FASE 1: Configuração Básica**

#### **1.1 Verificar Estrutura de Arquivos**
```bash
# Verificar se todos os arquivos foram criados
ls -la src/frontend/renderer/
ls -la src/frontend/renderer/components/containers/
ls -la src/frontend/renderer/pages/
ls -la src/frontend/renderer/routes/
```

**Arquivos esperados:**
- [ ] `src/frontend/renderer/App.tsx`
- [ ] `src/frontend/renderer/index.tsx`
- [ ] `src/frontend/renderer/index.html`
- [ ] `src/frontend/renderer/styles/globals.css`
- [ ] `src/frontend/renderer/components/layout/Layout.tsx`
- [ ] `src/frontend/renderer/components/containers/` (todos os componentes)
- [ ] `src/frontend/renderer/pages/` (todas as páginas)
- [ ] `src/frontend/renderer/routes/index.tsx`
- [ ] `vite.config.ts`
- [ ] `tailwind.config.js`
- [ ] `package.json`

#### **1.2 Instalar Dependências**
```bash
# Instalar dependências do projeto
npm install

# Verificar se não há conflitos
npm audit
```

#### **1.3 Verificar Configurações**
```bash
# Verificar TypeScript
npx tsc --noEmit

# Verificar ESLint (se configurado)
npx eslint src/frontend/renderer --ext .ts,.tsx
```

---

### **✅ FASE 2: Teste de Desenvolvimento**

#### **2.1 Iniciar Servidor de Desenvolvimento**
```bash
# Iniciar apenas o renderer (React)
npm run dev:renderer

# Ou iniciar completo (se Electron estiver configurado)
npm run dev
```

**Verificações:**
- [ ] Servidor inicia sem erros
- [ ] Aplicação carrega no navegador (http://localhost:3000)
- [ ] Console não mostra erros críticos
- [ ] Hot reload funciona

#### **2.2 Testar Navegação**
**Rotas para testar:**
- [ ] `/` - Dashboard principal
- [ ] `/containers` - Dashboard de containers
- [ ] `/containers/list` - Lista de containers
- [ ] `/containers/images` - Gerenciamento de imagens
- [ ] `/containers/engines` - Configuração de engines
- [ ] `/packages` - Gerenciador de pacotes
- [ ] `/settings` - Configurações
- [ ] `/about` - Sobre

**Verificações:**
- [ ] Todas as rotas carregam sem erro
- [ ] Navegação entre páginas funciona
- [ ] Breadcrumb atualiza corretamente
- [ ] Sidebar destaca item ativo

#### **2.3 Testar Componentes de Container**
**Componentes para verificar:**
- [ ] `ContainerDashboard` renderiza
- [ ] `ContainerCard` exibe placeholder ou dados
- [ ] `ContainerList` mostra tabela
- [ ] `ContainerImages` carrega interface
- [ ] `ContainerEngines` mostra configurações
- [ ] `ContainerSidebar` funciona
- [ ] `ContainerToolbar` responde a interações

---

### **✅ FASE 3: Teste de Funcionalidades**

#### **3.1 Testar Hooks de Container**
```typescript
// Teste manual no console do navegador
window.containerAPI = {
  // Simular dados para teste
  getContainers: () => Promise.resolve([]),
  getImages: () => Promise.resolve([]),
  getEngines: () => Promise.resolve([])
};
```

#### **3.2 Testar Estados de Loading**
- [ ] Spinners aparecem durante carregamento
- [ ] Estados de erro são exibidos adequadamente
- [ ] Estados vazios mostram mensagens apropriadas

#### **3.3 Testar Interações**
- [ ] Botões respondem a cliques
- [ ] Modais abrem e fecham
- [ ] Formulários validam entrada
- [ ] Filtros e busca funcionam
- [ ] Seleção múltipla funciona

---

### **✅ FASE 4: Teste de Integração com Backend**

#### **4.1 Verificar Comunicação Electron**
```javascript
// No console do navegador
console.log('Electron API:', window.electronAPI);
console.log('Container API:', window.containerAPI);
```

#### **4.2 Testar Chamadas de API**
```typescript
// Teste de chamada real (se backend estiver rodando)
fetch('/api/containers')
  .then(res => res.json())
  .then(data => console.log('Containers:', data))
  .catch(err => console.error('Erro:', err));
```

#### **4.3 Verificar Handlers IPC**
```javascript
// Testar handlers Electron (se disponível)
if (window.electronAPI) {
  window.electronAPI.getContainers()
    .then(containers => console.log('Containers via IPC:', containers))
    .catch(err => console.error('Erro IPC:', err));
}
```

---

### **✅ FASE 5: Teste de Build**

#### **5.1 Build de Produção**
```bash
# Build do renderer
npm run build:renderer

# Verificar arquivos gerados
ls -la dist/renderer/
```

#### **5.2 Preview de Produção**
```bash
# Testar build em servidor local
npm run preview
```

#### **5.3 Build Completo (se Electron configurado)**
```bash
# Build completo
npm run build

# Build para distribuição
npm run build:production
```

---

## 🐛 **Troubleshooting Comum**

### **Erro: Module not found**
```bash
# Verificar se todas as dependências estão instaladas
npm install

# Limpar cache
rm -rf node_modules/.vite
npm run dev
```

### **Erro: TypeScript**
```bash
# Verificar tipos
npx tsc --noEmit

# Instalar tipos faltantes
npm install @types/react @types/react-dom
```

### **Erro: Tailwind CSS não carrega**
```bash
# Verificar se PostCSS está configurado
npm install -D postcss autoprefixer

# Verificar tailwind.config.js
```

### **Erro: React Query**
```bash
# Verificar versão compatível
npm install @tanstack/react-query@^4.35.0
```

---

## 📊 **Critérios de Sucesso**

### **✅ Integração Básica**
- [ ] Aplicação inicia sem erros
- [ ] Todas as rotas funcionam
- [ ] Componentes renderizam
- [ ] Estilos aplicam corretamente

### **✅ Funcionalidade**
- [ ] Navegação funciona
- [ ] Estados de loading/erro funcionam
- [ ] Interações básicas respondem
- [ ] Layout responsivo

### **✅ Performance**
- [ ] Carregamento inicial < 3s
- [ ] Navegação entre páginas < 500ms
- [ ] Sem vazamentos de memória
- [ ] Bundle size razoável

### **✅ Qualidade**
- [ ] Sem erros no console
- [ ] Sem warnings críticos
- [ ] TypeScript compila sem erros
- [ ] Código segue padrões

---

## 🚀 **Próximos Passos Após Integração**

### **Se Tudo Funcionar:**
1. ✅ Marcar Fase 1 como completa
2. 🔄 Iniciar Fase 2: Funcionalidades Críticas
3. 📝 Documentar problemas encontrados
4. 🎯 Priorizar próximas implementações

### **Se Houver Problemas:**
1. 🐛 Documentar erros específicos
2. 🔍 Investigar causa raiz
3. 🛠️ Aplicar correções
4. 🔄 Re-testar integração

---

## 📞 **Comandos Úteis para Debug**

```bash
# Verificar versões
node --version
npm --version

# Limpar tudo e reinstalar
rm -rf node_modules package-lock.json
npm install

# Debug do Vite
npm run dev -- --debug

# Verificar bundle
npm run build -- --analyze

# Testar TypeScript
npx tsc --noEmit --skipLibCheck
```

---

**🎯 Meta: Ter a integração básica funcionando perfeitamente antes de prosseguir para as funcionalidades avançadas!**
