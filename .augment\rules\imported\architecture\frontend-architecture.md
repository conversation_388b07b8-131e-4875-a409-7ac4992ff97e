---
type: "agent_requested"
description: "Example description"
---
# 🎨 Arquitetura Frontend - Auto-Instalador V3 Lite

**📅 Data de Criação:** 05 de Agosto de 2025  
**🔗 Versão:** 1.0.0  
**👤 Preparado por:** Engenheiro de Software Sênior  
**🎯 Objetivo:** Documentar a arquitetura frontend completa  

---

## 🌟 **VISÃO GERAL FRONTEND**

O frontend do Auto-Instalador V3 Lite é construído com uma arquitetura moderna que combina:
- **Electron 37.1.2:** Framework desktop multiplataforma
- **React 19.2.0:** Biblioteca UI com novos recursos
- **TypeScript 5.6.2:** Tipagem estática
- **Vite 5.4.2:** Build tool otimizado
- **Tailwind CSS 4.0.0-beta.1:** Framework CSS utilitário

### **Arquitetura em Camadas**
```
┌─────────────────────────────────────────────────────────────┐
│                    ELECTRON MAIN PROCESS                    │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 WINDOW MANAGEMENT                       │ │
│  │  - BrowserWindow creation and management                │ │
│  │  - Menu and system tray integration                    │ │
│  │  - Auto-updater and native notifications               │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   IPC HANDLERS                          │ │
│  │  - Container operations bridge                          │ │
│  │  - File system access                                  │ │
│  │  - System information gathering                        │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              │ IPC Communication
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  ELECTRON RENDERER PROCESS                  │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                    PRESENTATION LAYER                   │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │     PAGES       │  │   COMPONENTS    │              │ │
│  │  │  - Dashboard    │  │  - UI Library   │              │ │
│  │  │  - Containers   │  │  - Features     │              │ │
│  │  │  - Settings     │  │  - Layout       │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   STATE MANAGEMENT                      │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │  REACT QUERY    │  │    ZUSTAND      │              │ │
│  │  │  - Server state │  │  - Client state │              │ │
│  │  │  - Caching      │  │  - UI state     │              │ │
│  │  │  - Mutations    │  │  - Preferences  │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  └─────────────────────────────────────────────────────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                   SERVICE LAYER                         │ │
│  │  ┌─────────────────┐  ┌─────────────────┐              │ │
│  │  │   API CLIENT    │  │   IPC BRIDGE    │              │ │
│  │  │  - HTTP calls   │  │  - Electron IPC │              │ │
│  │  │  - Error handle │  │  - Type safety  │              │ │
│  │  │  - Interceptors │  │  - Event system │              │ │
│  │  └─────────────────┘  └─────────────────┘              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## ⚡ **ELECTRON MAIN PROCESS**

### **Responsabilidades Principais**
- **Gerenciamento de Janelas:** Criação, controle e lifecycle
- **IPC Handlers:** Ponte segura com renderer process
- **Acesso Nativo:** APIs do sistema operacional
- **Auto-updater:** Sistema de atualização automática
- **System Tray:** Integração com bandeja do sistema

### **Estrutura de Arquivos**
```
src/frontend/electron/main/
├── main.ts                     # Entry point principal
├── window-manager.ts           # Gerenciamento de janelas
├── ipc-handlers/               # Handlers IPC organizados
│   ├── container-handlers.ts   # Operações de container
│   ├── file-handlers.ts        # Operações de arquivo
│   ├── system-handlers.ts      # Informações do sistema
│   └── index.ts               # Exportações centralizadas
├── services/                   # Serviços do main process
│   ├── auto-updater.ts        # Sistema de atualização
│   ├── system-tray.ts         # System tray
│   ├── menu-builder.ts        # Construção de menus
│   └── notification.ts        # Notificações nativas
└── utils/                      # Utilitários do main process
    ├── security.ts            # Configurações de segurança
    ├── paths.ts               # Gerenciamento de caminhos
    └── logger.ts              # Sistema de logging
```

### **Configurações de Segurança**
```typescript
// Configuração segura do BrowserWindow
const mainWindow = new BrowserWindow({
  webPreferences: {
    nodeIntegration: false,           // Desabilita Node.js no renderer
    contextIsolation: true,           // Habilita isolamento de contexto
    enableRemoteModule: false,        // Desabilita módulo remote
    preload: path.join(__dirname, 'preload.js'), // Script preload seguro
    sandbox: true,                    // Habilita sandbox
    webSecurity: true                 // Habilita segurança web
  }
});
```

---

## 🎯 **ELECTRON RENDERER PROCESS**

### **React 19.2.0 - Novos Recursos**
- **Actions:** Funções assíncronas para formulários
- **useOptimistic:** Updates otimistas de UI
- **use() Hook:** Leitura de recursos assíncronos
- **Concurrent Features:** Renderização concorrente

### **Estrutura de Componentes**
```
src/frontend/renderer/
├── App.tsx                     # Componente raiz
├── components/                 # Componentes organizados
│   ├── ui/                    # Componentes base reutilizáveis
│   │   ├── Button.tsx         # Botão customizado
│   │   ├── Input.tsx          # Input customizado
│   │   ├── Modal.tsx          # Modal reutilizável
│   │   ├── Table.tsx          # Tabela com features
│   │   └── index.ts           # Exportações UI
│   ├── layout/                # Componentes de layout
│   │   ├── Header.tsx         # Cabeçalho da aplicação
│   │   ├── Sidebar.tsx        # Barra lateral
│   │   ├── Footer.tsx         # Rodapé
│   │   └── Layout.tsx         # Layout principal
│   ├── features/              # Componentes de features
│   │   ├── containers/        # Gerenciamento de containers
│   │   │   ├── ContainerDashboard.tsx
│   │   │   ├── ContainerList.tsx
│   │   │   ├── ContainerStats.tsx
│   │   │   ├── ContainerLogs.tsx
│   │   │   └── index.ts
│   │   ├── settings/          # Configurações
│   │   └── dashboard/         # Dashboard principal
│   └── forms/                 # Componentes de formulário
│       ├── ContainerForm.tsx  # Formulário de container
│       ├── SettingsForm.tsx   # Formulário de configurações
│       └── index.ts
├── pages/                     # Páginas da aplicação
│   ├── Dashboard.tsx          # Página principal
│   ├── Containers.tsx         # Página de containers
│   ├── Settings.tsx           # Página de configurações
│   └── index.ts
├── hooks/                     # Custom hooks
│   ├── useContainers.ts       # Hook para containers
│   ├── useSettings.ts         # Hook para configurações
│   ├── useIPC.ts             # Hook para IPC
│   └── index.ts
├── services/                  # Serviços
│   ├── api/                   # Cliente API
│   │   ├── containers.ts      # API de containers
│   │   ├── settings.ts        # API de configurações
│   │   ├── client.ts          # Cliente HTTP base
│   │   └── index.ts
│   ├── ipc/                   # Bridge IPC
│   │   ├── container-bridge.ts # Bridge de containers
│   │   ├── system-bridge.ts   # Bridge de sistema
│   │   └── index.ts
│   └── storage/               # Armazenamento local
│       ├── local-storage.ts   # LocalStorage wrapper
│       └── index.ts
├── store/                     # Estado global
│   ├── slices/                # Slices do Zustand
│   │   ├── containers.ts      # Estado de containers
│   │   ├── settings.ts        # Estado de configurações
│   │   ├── ui.ts             # Estado da UI
│   │   └── index.ts
│   ├── providers/             # Providers React Query
│   │   ├── query-client.ts    # Configuração React Query
│   │   └── index.ts
│   └── index.ts
├── types/                     # Tipos TypeScript
│   ├── api.ts                # Tipos da API
│   ├── containers.ts         # Tipos de containers
│   ├── electron.ts           # Tipos Electron
│   └── index.ts
├── utils/                     # Utilitários
│   ├── formatters.ts         # Formatadores
│   ├── validators.ts         # Validadores
│   ├── constants.ts          # Constantes
│   └── index.ts
└── styles/                    # Estilos
    ├── globals.css           # Estilos globais
    ├── components.css        # Estilos de componentes
    └── tailwind.css          # Configuração Tailwind
```

---

## 🔄 **GERENCIAMENTO DE ESTADO**

### **React Query 5.56.2 - Server State**
```typescript
// Configuração do Query Client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,      // 5 minutos
      cacheTime: 10 * 60 * 1000,     // 10 minutos
      retry: 3,                       // 3 tentativas
      refetchOnWindowFocus: false,    // Não refetch no foco
    },
    mutations: {
      retry: 1,                       // 1 tentativa para mutations
    },
  },
});

// Hook customizado para containers
export const useContainers = (options?: UseContainersOptions) => {
  return useQuery({
    queryKey: ['containers', options],
    queryFn: () => containerApi.getAll(options),
    enabled: !!options,
    refetchInterval: 5000, // Refetch a cada 5 segundos
  });
};
```

### **Zustand 5.0.0 - Client State**
```typescript
// Store de configurações da UI
interface UIStore {
  theme: 'light' | 'dark' | 'system';
  sidebarCollapsed: boolean;
  notifications: Notification[];
  setTheme: (theme: UIStore['theme']) => void;
  toggleSidebar: () => void;
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
}

export const useUIStore = create<UIStore>((set, get) => ({
  theme: 'system',
  sidebarCollapsed: false,
  notifications: [],
  
  setTheme: (theme) => set({ theme }),
  toggleSidebar: () => set({ sidebarCollapsed: !get().sidebarCollapsed }),
  addNotification: (notification) => set({
    notifications: [...get().notifications, { ...notification, id: nanoid() }]
  }),
  removeNotification: (id) => set({
    notifications: get().notifications.filter(n => n.id !== id)
  }),
}));
```

---

## 🎨 **SISTEMA DE DESIGN**

### **Tailwind CSS 4.0.0-beta.1**
```css
/* Configuração customizada */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Variáveis CSS customizadas */
:root {
  --color-primary: 59 130 246;      /* blue-500 */
  --color-secondary: 107 114 128;   /* gray-500 */
  --color-success: 34 197 94;       /* green-500 */
  --color-warning: 245 158 11;      /* amber-500 */
  --color-error: 239 68 68;         /* red-500 */
}

/* Componentes customizados */
@layer components {
  .btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-lg transition-colors;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700;
  }
}
```

### **Framer Motion 11.5.4 - Animações**
```typescript
// Configurações de animação padrão
export const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 },
  transition: { duration: 0.2 }
};

export const slideIn = {
  initial: { x: -300, opacity: 0 },
  animate: { x: 0, opacity: 1 },
  exit: { x: -300, opacity: 0 },
  transition: { type: "spring", stiffness: 300, damping: 30 }
};

// Componente animado
const AnimatedCard = ({ children }: { children: React.ReactNode }) => (
  <motion.div
    variants={fadeInUp}
    initial="initial"
    animate="animate"
    exit="exit"
    className="card p-6"
  >
    {children}
  </motion.div>
);
```

---

## 🔧 **CONFIGURAÇÃO E BUILD**

### **Vite 5.4.2 - Build Tool**
```typescript
// vite.config.ts
export default defineConfig({
  plugins: [
    react(),
    tsconfigPaths(),
  ],
  build: {
    outDir: 'dist/renderer',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
      },
    },
    chunkSizeWarningLimit: 1000,
  },
  server: {
    port: 3000,
    strictPort: true,
  },
  optimizeDeps: {
    include: ['react', 'react-dom', '@tanstack/react-query'],
  },
});
```

### **TypeScript 5.6.2 - Configuração**
```json
{
  "compilerOptions": {
    "target": "ES2022",
    "lib": ["ES2022", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    "strict": true,
    "jsx": "react-jsx",
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"],
      "@/components/*": ["src/components/*"],
      "@/hooks/*": ["src/hooks/*"],
      "@/services/*": ["src/services/*"],
      "@/types/*": ["src/types/*"],
      "@/utils/*": ["src/utils/*"]
    }
  }
}
```

---

**📝 Preparado por:** Engenheiro de Software Sênior  
**📅 Data:** 05 de Agosto de 2025  
**🔄 Versão:** 1.0.0 - Arquitetura Frontend  
**✨ Status:** Documentação Completa**
