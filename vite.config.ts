/**
 * Vite Configuration - Auto-Instalador V3 Lite
 * 
 * @description Configuração do Vite para desenvolvimento e build
 * <AUTHOR> Agent
 * @date 2025-08-05
 */

import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { rmSync } from 'fs';

// Limpar dist antes do build
if (process.env.NODE_ENV === 'production') {
  rmSync('dist/renderer', { recursive: true, force: true });
}

export default defineConfig(({ command, mode }) => {
  const isProduction = mode === 'production';
  const isDevelopment = command === 'serve';

  return {
    plugins: [
      react({
        // Otimizações para React 19.2
        jsxRuntime: 'automatic',
        jsxImportSource: 'react',
        fastRefresh: isDevelopment,
        babel: {
          plugins: [
            // Suporte para React 19.2 features
            ...(isDevelopment ? [] : [
              ['babel-plugin-transform-remove-console', { exclude: ['error', 'warn'] }]
            ])
          ]
        }
      }),

      // Plugin customizado para Electron
      {
        name: 'electron-renderer',
        configureServer(server) {
          server.middlewares.use('/electron-api', (req, res, next) => {
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            next();
          });
        },
        config(config, { command }) {
          if (command === 'serve') {
            config.define = {
              ...config.define,
              __ELECTRON_DEV__: true,
              __DEV__: true
            };
          }
        }
      }
    ],

    // Configuração base
    base: './',
    root: 'src/frontend/renderer',
    publicDir: resolve(__dirname, 'assets'),

    // Build configuration
    build: {
      outDir: resolve(__dirname, 'dist/renderer'),
      emptyOutDir: true,
      target: 'esnext',
      minify: isProduction ? 'terser' : false,
      sourcemap: !isProduction,

      rollupOptions: {
        input: resolve(__dirname, 'src/frontend/renderer/index.html'),
        external: ['electron'],
        
        output: {
          manualChunks: {
            // Separar vendor chunks para melhor caching
            'react-vendor': ['react', 'react-dom'],
            'router-vendor': ['react-router-dom'],
            'query-vendor': ['@tanstack/react-query'],
            'ui-vendor': ['react-hot-toast'],
            'utils-vendor': ['clsx']
          }
        }
      },

      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction,
          pure_funcs: isProduction ? ['console.log', 'console.info'] : []
        },
        mangle: {
          safari10: true
        }
      }
    },

    // Server configuration
    server: {
      port: 3000,
      strictPort: true,
      cors: true,
      
      // Configuração específica para Electron
      headers: {
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin'
      },

      // Watch configuration otimizada para SSD
      watch: {
        usePolling: false,
        interval: 100,
        binaryInterval: 300,
        ignored: ['**/node_modules/**', '**/.git/**', '**/dist/**']
      }
    },

    // Resolve configuration
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/frontend/renderer'),
        '@components': resolve(__dirname, 'src/frontend/renderer/components'),
        '@pages': resolve(__dirname, 'src/frontend/renderer/pages'),
        '@services': resolve(__dirname, 'src/frontend/renderer/services'),
        '@utils': resolve(__dirname, 'src/frontend/renderer/utils'),
        '@types': resolve(__dirname, 'shared/types'),
        '@assets': resolve(__dirname, 'assets')
      }
    },

    // CSS configuration
    css: {
      postcss: {
        plugins: [
          require('tailwindcss'),
          require('autoprefixer')
        ]
      },
      devSourcemap: !isProduction
    },

    // Define global constants
    define: {
      __DEV__: isDevelopment,
      __PROD__: isProduction,
      __VERSION__: JSON.stringify('3.0.0-lite'),
      __BUILD_DATE__: JSON.stringify(new Date().toISOString()),
      global: 'globalThis'
    },

    // Optimizations
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@tanstack/react-query',
        'react-hot-toast'
      ],
      exclude: ['electron']
    },

    // Cache configuration
    cacheDir: 'node_modules/.vite',

    // Preview configuration (for production testing)
    preview: {
      port: 3001,
      strictPort: true,
      cors: true
    }
  };
});
